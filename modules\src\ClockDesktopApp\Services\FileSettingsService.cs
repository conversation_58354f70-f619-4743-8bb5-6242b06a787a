using System.IO;
using System.Text.Json;
using System.Windows.Media;
using Microsoft.Extensions.Logging;

namespace ArtDesignFramework.ClockDesktopApp.Services;

/// <summary>
/// File-based settings storage implementation
/// </summary>
public class FileSettingsService : ISettingsStorageService
{
    private readonly ILogger<FileSettingsService> _logger;
    private readonly string _settingsDirectory;

    public FileSettingsService(ILogger<FileSettingsService> logger)
    {
        _logger = logger;

        // Create settings directory in user's documents
        var documentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
        _settingsDirectory = Path.Combine(documentsPath, "ArtDesignFramework", "ClockDesigns");

        // Ensure directory exists
        Directory.CreateDirectory(_settingsDirectory);
    }

    /// <summary>
    /// Saves a clock design to file
    /// Last Updated: 2025-01-11 18:30:00 UTC
    /// </summary>
    /// <param name="settings">Clock settings to save</param>
    /// <param name="fileName">Optional file name</param>
    [TestableMethod("FilePersistence", IncludePerformanceTests = true, IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 500)]
    public async Task SaveDesignAsync(ClockSettings settings, string? fileName = null)
    {
        try
        {
            fileName ??= "default";
            var filePath = Path.Combine(_settingsDirectory, $"{fileName}.json");

            // Convert to serializable format
            var serializableSettings = ConvertToSerializable(settings);

            var json = JsonSerializer.Serialize(serializableSettings, new JsonSerializerOptions
            {
                WriteIndented = true
            });

            await File.WriteAllTextAsync(filePath, json);
            _logger.LogInformation("Clock design saved to {FilePath}", filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save clock design to file");
            throw;
        }
    }

    /// <summary>
    /// Loads a clock design from file
    /// Last Updated: 2025-01-11 18:30:00 UTC
    /// </summary>
    /// <param name="fileName">Optional file name to load</param>
    /// <returns>Loaded clock settings or null if not found</returns>
    [TestableMethod("FileRetrieval", IncludePerformanceTests = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 300)]
    public async Task<ClockSettings?> LoadDesignAsync(string? fileName = null)
    {
        try
        {
            fileName ??= "default";
            var filePath = Path.Combine(_settingsDirectory, $"{fileName}.json");

            if (!File.Exists(filePath))
            {
                _logger.LogWarning("Settings file not found: {FilePath}", filePath);
                return null;
            }

            var json = await File.ReadAllTextAsync(filePath);
            var serializableSettings = JsonSerializer.Deserialize<SerializableClockSettings>(json);

            if (serializableSettings == null)
            {
                _logger.LogWarning("Failed to deserialize settings from {FilePath}", filePath);
                return null;
            }

            var settings = ConvertFromSerializable(serializableSettings);
            _logger.LogInformation("Clock design loaded from {FilePath}", filePath);
            return settings;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load clock design from file");
            return null;
        }
    }

    /// <summary>
    /// Gets all available design files
    /// Last Updated: 2025-01-11 18:30:00 UTC
    /// </summary>
    /// <returns>List of available design file names</returns>
    [TestableMethod("FileEnumeration", IncludePerformanceTests = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 200)]
    public async Task<List<string>> GetAvailableDesignsAsync()
    {
        try
        {
            await Task.CompletedTask; // Make it async for interface compatibility

            var files = Directory.GetFiles(_settingsDirectory, "*.json")
                .Select(Path.GetFileNameWithoutExtension)
                .Where(name => !string.IsNullOrEmpty(name))
                .Cast<string>()
                .ToList();

            _logger.LogInformation("Found {Count} design files", files.Count);
            return files;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get available designs");
            return new List<string>();
        }
    }

    /// <summary>
    /// Deletes a design file
    /// Last Updated: 2025-01-11 18:30:00 UTC
    /// </summary>
    /// <param name="fileName">Name of the file to delete</param>
    /// <returns>True if deletion was successful</returns>
    [TestableMethod("FileDeletion", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 100)]
    public async Task<bool> DeleteDesignAsync(string fileName)
    {
        try
        {
            await Task.CompletedTask; // Make it async for interface compatibility

            var filePath = Path.Combine(_settingsDirectory, $"{fileName}.json");

            if (!File.Exists(filePath))
            {
                _logger.LogWarning("Design file not found for deletion: {FilePath}", filePath);
                return false;
            }

            File.Delete(filePath);
            _logger.LogInformation("Design file deleted: {FilePath}", filePath);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete design file: {FileName}", fileName);
            return false;
        }
    }

    /// <summary>
    /// File storage is always available
    /// Last Updated: 2025-01-11 18:30:00 UTC
    /// </summary>
    /// <returns>Always returns true for file storage</returns>
    [TestableMethod("StorageAvailability", ExpectedExecutionTimeMs = 10)]
    public async Task<bool> IsAvailableAsync()
    {
        await Task.CompletedTask; // Make it async for interface compatibility
        return true;
    }

    /// <summary>
    /// Converts ClockSettings to serializable format (public for ServerSettingsService)
    /// </summary>
    public SerializableClockSettings ConvertToSerializable(ClockSettings settings)
    {
        return new SerializableClockSettings
        {
            AlwaysOnTop = settings.AlwaysOnTop,
            ShowDate = settings.ShowDate,
            ShowAmPm = settings.ShowAmPm,
            Use24Hour = settings.Use24Hour,
            FontFamily = settings.FontFamily,
            FontSize = settings.FontSize,
            TextColor = BrushToString(settings.TextColor),
            DateColor = BrushToString(settings.DateColor),
            TextOpacity = settings.TextOpacity,
            StrokeOpacity = settings.StrokeOpacity,
            DateFormat = settings.DateFormat,
            DateFontSize = settings.DateFontSize,
            DateFontFamily = settings.DateFontFamily,
            DateOffsetX = settings.DateOffsetX,
            DateOffsetY = settings.DateOffsetY,
            ShowSeconds = settings.ShowSeconds,
            TimeFormat = settings.TimeFormat,
            SecondsFormat = settings.SecondsFormat,
            ShowAmPmSeparate = settings.ShowAmPmSeparate,
            AmPmColor = BrushToString(settings.AmPmColor),
            AmPmFontSize = settings.AmPmFontSize,
            AmPmFontFamily = settings.AmPmFontFamily,
            AmPmOffsetX = settings.AmPmOffsetX,
            AmPmOffsetY = settings.AmPmOffsetY,
            TransparentBackground = settings.TransparentBackground,
            BackgroundColor = BrushToString(settings.BackgroundColor),
            BackgroundImagePath = settings.BackgroundImagePath,
            BackgroundShape = settings.BackgroundShape,
            BackgroundCornerRadius = settings.BackgroundCornerRadius,
            BackgroundFeather = settings.BackgroundFeather,
            BackgroundPadding = settings.BackgroundPadding,
            BackgroundWidth = settings.BackgroundWidth,
            BackgroundHeight = settings.BackgroundHeight,
            BackgroundOffsetX = settings.BackgroundOffsetX,
            BackgroundOffsetY = settings.BackgroundOffsetY,
            EnableGlow = settings.EnableGlow,
            GlowColor = BrushToString(settings.GlowColor),
            GlowRadius = settings.GlowRadius,
            EnableShadow = settings.EnableShadow,
            ShadowColor = BrushToString(settings.ShadowColor),
            ShadowOffsetX = settings.ShadowOffsetX,
            ShadowOffsetY = settings.ShadowOffsetY,
            ShadowBlur = settings.ShadowBlur,
            EnableStroke = settings.EnableStroke,
            StrokeColor = BrushToString(settings.StrokeColor),
            StrokeWidth = settings.StrokeWidth,
            Enable3D = settings.Enable3D,
            TextDepth = settings.TextDepth,
            BevelSize = settings.BevelSize,
            Text3DColor = BrushToString(settings.Text3DColor),
            Width = (int)settings.Width,
            Height = (int)settings.Height,
            Left = (int)settings.Left,
            Top = (int)settings.Top
        };
    }

    /// <summary>
    /// Converts serializable format to ClockSettings (public for ServerSettingsService)
    /// </summary>
    public ClockSettings ConvertFromSerializable(SerializableClockSettings settings)
    {
        return new ClockSettings
        {
            AlwaysOnTop = settings.AlwaysOnTop,
            ShowDate = settings.ShowDate,
            ShowAmPm = settings.ShowAmPm,
            Use24Hour = settings.Use24Hour,
            FontFamily = settings.FontFamily,
            FontSize = settings.FontSize,
            TextColor = StringToBrush(settings.TextColor),
            DateColor = StringToBrush(settings.DateColor),
            TextOpacity = settings.TextOpacity,
            StrokeOpacity = settings.StrokeOpacity,
            DateFormat = settings.DateFormat,
            DateFontSize = settings.DateFontSize,
            DateFontFamily = settings.DateFontFamily,
            DateOffsetX = settings.DateOffsetX,
            DateOffsetY = settings.DateOffsetY,
            ShowSeconds = settings.ShowSeconds,
            TimeFormat = settings.TimeFormat,
            SecondsFormat = settings.SecondsFormat,
            ShowAmPmSeparate = settings.ShowAmPmSeparate,
            AmPmColor = StringToBrush(settings.AmPmColor),
            AmPmFontSize = settings.AmPmFontSize,
            AmPmFontFamily = settings.AmPmFontFamily,
            AmPmOffsetX = settings.AmPmOffsetX,
            AmPmOffsetY = settings.AmPmOffsetY,
            TransparentBackground = settings.TransparentBackground,
            BackgroundColor = StringToBrush(settings.BackgroundColor),
            BackgroundImagePath = settings.BackgroundImagePath,
            BackgroundShape = settings.BackgroundShape,
            BackgroundCornerRadius = settings.BackgroundCornerRadius,
            BackgroundFeather = settings.BackgroundFeather,
            BackgroundPadding = settings.BackgroundPadding,
            BackgroundWidth = settings.BackgroundWidth,
            BackgroundHeight = settings.BackgroundHeight,
            BackgroundOffsetX = settings.BackgroundOffsetX,
            BackgroundOffsetY = settings.BackgroundOffsetY,
            EnableGlow = settings.EnableGlow,
            GlowColor = StringToBrush(settings.GlowColor),
            GlowRadius = settings.GlowRadius,
            EnableShadow = settings.EnableShadow,
            ShadowColor = StringToBrush(settings.ShadowColor),
            ShadowOffsetX = settings.ShadowOffsetX,
            ShadowOffsetY = settings.ShadowOffsetY,
            ShadowBlur = settings.ShadowBlur,
            EnableStroke = settings.EnableStroke,
            StrokeColor = StringToBrush(settings.StrokeColor),
            StrokeWidth = settings.StrokeWidth,
            Enable3D = settings.Enable3D,
            TextDepth = settings.TextDepth,
            BevelSize = settings.BevelSize,
            Text3DColor = StringToBrush(settings.Text3DColor),
            Width = settings.Width,
            Height = settings.Height,
            Left = settings.Left,
            Top = settings.Top
        };
    }

    private string BrushToString(System.Windows.Media.Brush brush)
    {
        if (brush is SolidColorBrush solidBrush)
        {
            var color = solidBrush.Color;
            return $"#{color.A:X2}{color.R:X2}{color.G:X2}{color.B:X2}";
        }
        return "#FFFFFFFF"; // Default to white
    }

    private System.Windows.Media.Brush StringToBrush(string colorString)
    {
        try
        {
            var converter = new BrushConverter();
            return (System.Windows.Media.Brush)(converter.ConvertFromString(colorString) ?? System.Windows.Media.Brushes.White);
        }
        catch
        {
            return System.Windows.Media.Brushes.White; // Default fallback
        }
    }
}

/// <summary>
/// Serializable version of ClockSettings for JSON storage
/// </summary>
public class SerializableClockSettings
{
    public bool AlwaysOnTop { get; set; }
    public bool ShowDate { get; set; } = true;
    public bool ShowAmPm { get; set; } = true;
    public bool Use24Hour { get; set; }
    public string FontFamily { get; set; } = "Arial";
    public double FontSize { get; set; } = 72;
    public string TextColor { get; set; } = "#FFFFFF";
    public string DateColor { get; set; } = "#CCCCCC";
    public double TextOpacity { get; set; } = 1.0;
    public double StrokeOpacity { get; set; } = 1.0;
    public string DateFormat { get; set; } = "dddd, MMMM d, yyyy";
    public double DateFontSize { get; set; } = 16;
    public string DateFontFamily { get; set; } = "Arial";
    public double DateOffsetX { get; set; } = 0;
    public double DateOffsetY { get; set; } = 30;
    public bool ShowSeconds { get; set; }
    public string TimeFormat { get; set; } = "HH:mm";
    public string SecondsFormat { get; set; } = ":ss";
    public bool ShowAmPmSeparate { get; set; }
    public string AmPmColor { get; set; } = "#CCCCCC";
    public double AmPmFontSize { get; set; } = 12;
    public string AmPmFontFamily { get; set; } = "Arial";
    public double AmPmOffsetX { get; set; } = 0;
    public double AmPmOffsetY { get; set; } = -10;
    public bool TransparentBackground { get; set; }
    public string BackgroundColor { get; set; } = "#000000";
    public string? BackgroundImagePath { get; set; }
    public string BackgroundShape { get; set; } = "Rectangle";
    public double BackgroundCornerRadius { get; set; } = 0;
    public double BackgroundFeather { get; set; } = 0;
    public double BackgroundPadding { get; set; } = 5;
    public double BackgroundWidth { get; set; } = 0;
    public double BackgroundHeight { get; set; } = 0;
    public double BackgroundOffsetX { get; set; } = 0;
    public double BackgroundOffsetY { get; set; } = 0;
    public bool EnableGlow { get; set; }
    public string GlowColor { get; set; } = "#FFFFFF";
    public double GlowRadius { get; set; } = 10;
    public bool EnableShadow { get; set; }
    public string ShadowColor { get; set; } = "#000000";
    public double ShadowOffsetX { get; set; } = 3;
    public double ShadowOffsetY { get; set; } = 3;
    public double ShadowBlur { get; set; } = 5;
    public bool EnableStroke { get; set; }
    public string StrokeColor { get; set; } = "#FFFFFF";
    public double StrokeWidth { get; set; } = 2;
    public bool Enable3D { get; set; }
    public double TextDepth { get; set; } = 15;
    public double BevelSize { get; set; } = 2;
    public string Text3DColor { get; set; } = "#C0C0C0";
    public int Width { get; set; } = 800;
    public int Height { get; set; } = 600;
    public int Left { get; set; } = 100;
    public int Top { get; set; } = 100;
}
