using ArtDesignFramework.ClockDesktopApp.Views;
using ArtDesignFramework.UserInterface.Services.Rendering;
using Microsoft.Extensions.Logging;

namespace ArtDesignFramework.ClockDesktopApp.Services;

/// <summary>
/// Service for creating and managing desktop clock widgets
/// </summary>
public class ClockWidgetService
{
    private readonly ILogger<ClockWidgetService> _logger;
    private readonly ISKPaintPool _paintPool;
    private readonly List<ClockWidgetWindow> _activeWidgets = new();

    public ClockWidgetService(ILogger<ClockWidgetService> logger, ISKPaintPool paintPool)
    {
        _logger = logger;
        _paintPool = paintPool ?? throw new ArgumentNullException(nameof(paintPool));
    }

    /// <summary>
    /// Creates a new desktop clock widget with the specified settings
    /// </summary>
    public async Task CreateDesktopWidgetAsync(ClockSettings settings)
    {
        try
        {
            _logger.LogInformation("Creating desktop clock widget...");

            // Create the widget window on the UI thread
            await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
            {
                var widget = new ClockWidgetWindow(settings, _paintPool);

                // Configure widget properties
                widget.Left = settings.Left;
                widget.Top = settings.Top;
                widget.Width = settings.Width;
                widget.Height = settings.Height;
                widget.Topmost = settings.AlwaysOnTop;
                widget.Opacity = settings.Opacity;

                // Show the widget
                widget.Show();

                // Track the widget
                _activeWidgets.Add(widget);

                // Handle widget closing
                widget.Closed += (s, e) =>
                {
                    _activeWidgets.Remove(widget);
                    _logger.LogInformation("Desktop widget closed");
                };

                _logger.LogInformation("Desktop widget created and displayed");
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create desktop widget");
            throw;
        }
    }

    /// <summary>
    /// Gets all currently active widgets
    /// </summary>
    public IReadOnlyList<ClockWidgetWindow> GetActiveWidgets()
    {
        return _activeWidgets.AsReadOnly();
    }

    /// <summary>
    /// Closes all active widgets
    /// </summary>
    public void CloseAllWidgets()
    {
        try
        {
            var widgetsToClose = _activeWidgets.ToList();
            foreach (var widget in widgetsToClose)
            {
                widget.Close();
            }
            _logger.LogInformation("All desktop widgets closed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error closing widgets");
        }
    }

    /// <summary>
    /// Updates settings for all active widgets
    /// </summary>
    public void UpdateAllWidgets(ClockSettings settings)
    {
        try
        {
            foreach (var widget in _activeWidgets)
            {
                widget.UpdateSettings(settings);
            }
            _logger.LogInformation("Updated settings for {Count} widgets", _activeWidgets.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating widgets");
        }
    }
}
