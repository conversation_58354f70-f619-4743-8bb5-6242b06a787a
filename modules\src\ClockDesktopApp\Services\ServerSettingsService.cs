using System.Net.Http;
using System.Net.Http.Json;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace ArtDesignFramework.ClockDesktopApp.Services;

/// <summary>
/// HTTP-based settings storage implementation with fallback to file storage
/// </summary>
public class ServerSettingsService : ISettingsStorageService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<ServerSettingsService> _logger;
    private readonly FileSettingsService _fallbackService;
    private readonly ServerSettingsOptions _options;

    public ServerSettingsService(
        HttpClient httpClient,
        ILogger<ServerSettingsService> logger,
        FileSettingsService fallbackService,
        IOptions<ServerSettingsOptions> options)
    {
        _httpClient = httpClient;
        _logger = logger;
        _fallbackService = fallbackService;
        _options = options.Value;
    }

    /// <summary>
    /// Saves clock design settings via HTTP API with fallback
    /// Last Updated: 2025-01-11 18:30:00 UTC
    /// </summary>
    /// <param name="settings">Clock settings to save</param>
    /// <param name="fileName">Optional file name</param>
    [TestableMethod("ServerPersistence", IncludePerformanceTests = true, IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 2000)]
    public async Task SaveDesignAsync(ClockSettings settings, string? fileName = null)
    {
        fileName ??= "default";

        try
        {
            if (await IsAvailableAsync())
            {
                await SaveToServerAsync(settings, fileName);
                _logger.LogInformation("Settings saved to server: {FileName}", fileName);
                return;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to save to server, falling back to file storage");
        }

        // Fallback to file storage
        await _fallbackService.SaveDesignAsync(settings, fileName);
        _logger.LogInformation("Settings saved to file storage (fallback): {FileName}", fileName);
    }

    /// <summary>
    /// Loads clock design settings via HTTP API with fallback
    /// Last Updated: 2025-01-11 18:30:00 UTC
    /// </summary>
    /// <param name="fileName">Optional file name to load</param>
    /// <returns>Loaded clock settings or null if not found</returns>
    [TestableMethod("ServerRetrieval", IncludePerformanceTests = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 1500)]
    public async Task<ClockSettings?> LoadDesignAsync(string? fileName = null)
    {
        fileName ??= "default";

        try
        {
            if (await IsAvailableAsync())
            {
                var settings = await LoadFromServerAsync(fileName);
                if (settings != null)
                {
                    _logger.LogInformation("Settings loaded from server: {FileName}", fileName);
                    return settings;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to load from server, falling back to file storage");
        }

        // Fallback to file storage
        var fallbackSettings = await _fallbackService.LoadDesignAsync(fileName);
        if (fallbackSettings != null)
        {
            _logger.LogInformation("Settings loaded from file storage (fallback): {FileName}", fileName);
        }
        return fallbackSettings;
    }

    /// <summary>
    /// Gets all available design names from server with fallback
    /// Last Updated: 2025-01-11 18:30:00 UTC
    /// </summary>
    /// <returns>List of available design names</returns>
    [TestableMethod("ServerEnumeration", IncludePerformanceTests = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 1000)]
    public async Task<List<string>> GetAvailableDesignsAsync()
    {
        try
        {
            if (await IsAvailableAsync())
            {
                var designs = await GetDesignsFromServerAsync();
                _logger.LogInformation("Retrieved {Count} designs from server", designs.Count);
                return designs;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get designs from server, falling back to file storage");
        }

        // Fallback to file storage
        var fallbackDesigns = await _fallbackService.GetAvailableDesignsAsync();
        _logger.LogInformation("Retrieved {Count} designs from file storage (fallback)", fallbackDesigns.Count);
        return fallbackDesigns;
    }

    /// <summary>
    /// Deletes a design from server with fallback
    /// Last Updated: 2025-01-11 18:30:00 UTC
    /// </summary>
    /// <param name="fileName">Name of the design to delete</param>
    /// <returns>True if deletion was successful</returns>
    [TestableMethod("ServerDeletion", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 1200)]
    public async Task<bool> DeleteDesignAsync(string fileName)
    {
        try
        {
            if (await IsAvailableAsync())
            {
                var success = await DeleteFromServerAsync(fileName);
                if (success)
                {
                    _logger.LogInformation("Design deleted from server: {FileName}", fileName);
                    return true;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to delete from server, falling back to file storage");
        }

        // Fallback to file storage
        var fallbackSuccess = await _fallbackService.DeleteDesignAsync(fileName);
        if (fallbackSuccess)
        {
            _logger.LogInformation("Design deleted from file storage (fallback): {FileName}", fileName);
        }
        return fallbackSuccess;
    }

    /// <summary>
    /// Checks if the server is available
    /// Last Updated: 2025-01-11 18:30:00 UTC
    /// </summary>
    /// <returns>True if server is available and responding</returns>
    [TestableMethod("ServerAvailability", IncludePerformanceTests = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 3000)]
    public async Task<bool> IsAvailableAsync()
    {
        try
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(_options.HealthCheckTimeoutSeconds));
            var response = await _httpClient.GetAsync("/api/status/health", cts.Token);
            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }

    private async Task SaveToServerAsync(ClockSettings settings, string fileName)
    {
        var serializableSettings = ConvertToSerializable(settings);
        var request = new SaveSettingsRequest
        {
            FileName = fileName,
            Settings = serializableSettings
        };

        using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(_options.RequestTimeoutSeconds));
        var response = await _httpClient.PostAsJsonAsync("/api/clock-settings", request, cts.Token);
        response.EnsureSuccessStatusCode();
    }

    private async Task<ClockSettings?> LoadFromServerAsync(string fileName)
    {
        using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(_options.RequestTimeoutSeconds));
        var response = await _httpClient.GetAsync($"/api/clock-settings/{fileName}", cts.Token);

        if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
        {
            return null;
        }

        response.EnsureSuccessStatusCode();
        var serializableSettings = await response.Content.ReadFromJsonAsync<SerializableClockSettings>(cancellationToken: cts.Token);
        return serializableSettings != null ? ConvertFromSerializable(serializableSettings) : null;
    }

    private async Task<List<string>> GetDesignsFromServerAsync()
    {
        using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(_options.RequestTimeoutSeconds));
        var response = await _httpClient.GetAsync("/api/clock-settings", cts.Token);
        response.EnsureSuccessStatusCode();

        var designs = await response.Content.ReadFromJsonAsync<List<string>>(cancellationToken: cts.Token);
        return designs ?? new List<string>();
    }

    private async Task<bool> DeleteFromServerAsync(string fileName)
    {
        using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(_options.RequestTimeoutSeconds));
        var response = await _httpClient.DeleteAsync($"/api/clock-settings/{fileName}", cts.Token);
        return response.IsSuccessStatusCode;
    }

    private SerializableClockSettings ConvertToSerializable(ClockSettings settings)
    {
        // Use the same conversion logic as the original SettingsService
        return _fallbackService.ConvertToSerializable(settings);
    }

    private ClockSettings ConvertFromSerializable(SerializableClockSettings settings)
    {
        // Use the same conversion logic as the original SettingsService
        return _fallbackService.ConvertFromSerializable(settings);
    }
}

/// <summary>
/// Configuration options for server settings service
/// </summary>
public class ServerSettingsOptions
{
    public const string SectionName = "ServerSettings";

    /// <summary>
    /// Base URL for the settings API
    /// </summary>
    public string BaseUrl { get; set; } = "http://localhost:5000";

    /// <summary>
    /// Timeout for health check requests in seconds
    /// </summary>
    public int HealthCheckTimeoutSeconds { get; set; } = 3;

    /// <summary>
    /// Timeout for API requests in seconds
    /// </summary>
    public int RequestTimeoutSeconds { get; set; } = 10;
}

/// <summary>
/// Request model for saving settings
/// </summary>
public class SaveSettingsRequest
{
    public string FileName { get; set; } = string.Empty;
    public SerializableClockSettings Settings { get; set; } = new();
}
