using System.IO;
using System.Text.Json;
using System.Windows.Media;
using Microsoft.Extensions.Logging;
using WpfBrush = System.Windows.Media.Brush;

namespace ArtDesignFramework.ClockDesktopApp.Services;

/// <summary>
/// Service for saving and loading clock design settings (now delegates to storage service)
/// </summary>
public class SettingsService
{
    private readonly ILogger<SettingsService> _logger;
    private readonly ISettingsStorageService _storageService;

    public SettingsService(ILogger<SettingsService> logger, ISettingsStorageService storageService)
    {
        _logger = logger;
        _storageService = storageService;
    }

    /// <summary>
    /// Saves a clock design using the configured storage service
    /// Last Updated: 2025-01-11 18:30:00 UTC
    /// </summary>
    /// <param name="settings">Clock settings to save</param>
    /// <param name="fileName">Optional file name</param>
    [TestableMethod("SettingsPersistence", IncludePerformanceTests = true, IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 1000)]
    public async Task SaveDesignAsync(ClockSettings settings, string? fileName = null)
    {
        try
        {
            await _storageService.SaveDesignAsync(settings, fileName);
            _logger.LogInformation("Clock design saved via storage service: {FileName}", fileName ?? "default");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save clock design via storage service");
            throw;
        }
    }

    /// <summary>
    /// Loads a clock design using the configured storage service
    /// Last Updated: 2025-01-11 18:30:00 UTC
    /// </summary>
    /// <param name="fileName">Optional file name to load</param>
    /// <returns>Loaded clock settings or null if not found</returns>
    [TestableMethod("SettingsRetrieval", IncludePerformanceTests = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 800)]
    public async Task<ClockSettings?> LoadDesignAsync(string? fileName = null)
    {
        try
        {
            var settings = await _storageService.LoadDesignAsync(fileName);
            if (settings != null)
            {
                _logger.LogInformation("Clock design loaded via storage service: {FileName}", fileName ?? "default");
            }
            return settings;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load clock design via storage service");
            return null;
        }
    }

    /// <summary>
    /// Gets all available design files using the configured storage service
    /// Last Updated: 2025-01-11 18:30:00 UTC
    /// </summary>
    /// <returns>List of available design names</returns>
    [TestableMethod("SettingsEnumeration", IncludePerformanceTests = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 500)]
    public async Task<List<string>> GetAvailableDesignsAsync()
    {
        try
        {
            var designs = await _storageService.GetAvailableDesignsAsync();
            _logger.LogInformation("Found {Count} designs via storage service", designs.Count);
            return designs;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get available designs via storage service");
            return new List<string>();
        }
    }

    /// <summary>
    /// Deletes a design using the configured storage service
    /// Last Updated: 2025-01-11 18:30:00 UTC
    /// </summary>
    /// <param name="fileName">Name of the design file to delete</param>
    /// <returns>True if deletion was successful</returns>
    [TestableMethod("SettingsDeletion", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 300)]
    public async Task<bool> DeleteDesignAsync(string fileName)
    {
        try
        {
            var success = await _storageService.DeleteDesignAsync(fileName);
            if (success)
            {
                _logger.LogInformation("Design deleted via storage service: {FileName}", fileName);
            }
            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete design via storage service: {FileName}", fileName);
            return false;
        }
    }

    /// <summary>
    /// Checks if the storage service is available
    /// Last Updated: 2025-01-11 18:30:00 UTC
    /// </summary>
    /// <returns>True if storage service is available</returns>
    [TestableMethod("StorageAvailability", IncludePerformanceTests = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 200)]
    public async Task<bool> IsStorageAvailableAsync()
    {
        try
        {
            return await _storageService.IsAvailableAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check storage service availability");
            return false;
        }
    }
}
