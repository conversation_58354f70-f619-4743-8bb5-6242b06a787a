<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <UseWindowsForms>true</UseWindowsForms>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <AssemblyTitle>ArtDesignFramework Advanced Clock Desktop App</AssemblyTitle>
    <AssemblyDescription>Advanced customizable desktop clock application with live preview and widget functionality</AssemblyDescription>

    <!-- <StartupObject>ArtDesignFramework.ClockDesktopApp.TestProgram</StartupObject> -->
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.0" />
    <PackageReference Include="SkiaSharp.Views.WPF" Version="2.88.8" />
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />

  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Core\ArtDesignFramework.Core.csproj" />
    <ProjectReference Include="..\TestFramework\ArtDesignFramework.TestFramework.csproj" />
    <ProjectReference Include="..\UserInterface\ArtDesignFramework.UserInterface.csproj" />
    <ProjectReference Include="..\Performance\ArtDesignFramework.Performance.csproj" />
    <ProjectReference Include="..\AILighting\ArtDesignFramework.AILighting.csproj" />
    <ProjectReference Include="..\AIModelManager\ArtDesignFramework.AIModelManager.csproj" />
    <ProjectReference Include="..\FreeFonts\ArtDesignFramework.FreeFonts.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Resources\**\*" />
  </ItemGroup>

</Project>
