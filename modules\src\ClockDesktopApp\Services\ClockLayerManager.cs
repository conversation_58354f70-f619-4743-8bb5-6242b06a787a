// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using ArtDesignFramework.ClockDesktopApp.Models;
using ArtDesignFramework.Core;

using Microsoft.Extensions.Logging;
using SkiaSharp;

namespace ArtDesignFramework.ClockDesktopApp.Services;

/// <summary>
/// Represents a clock layer with rendering data
/// </summary>
public class ClockLayerData
{
    public ClockLayer Layer { get; set; } = new("Default", ClockLayerType.Background);
    public SKBitmap? RenderedContent { get; set; }
    public SKRect Bounds { get; set; }
    public bool NeedsRedraw { get; set; } = true;
}

/// <summary>
/// Context data for clock rendering operations
/// </summary>
public class ClockRenderContext
{
    public DateTime CurrentTime { get; set; }
    public ClockSettings? Settings { get; set; }
    public int Width { get; set; }
    public int Height { get; set; }
    public object? ViewModel { get; set; }
}

/// <summary>
/// Service for managing clock layers with advanced blending and composition
/// </summary>
[Testable]
public class ClockLayerManager : IDisposable
{
    private readonly ILogger<ClockLayerManager> _logger;
    private readonly ILayerManager _coreLayerManager;
    private readonly Dictionary<Guid, ClockLayerData> _layerData = new();
    private readonly ObservableCollection<ClockLayer> _clockLayers = new();
    private bool _disposed;

    // Events for UI updates
    public event Action<ClockLayer>? LayerAdded;
    public event Action<ClockLayer>? LayerRemoved;
    public event Action<ClockLayer>? LayerChanged;
    public event Action? LayersReordered;

    /// <summary>
    /// Gets the clock layers ordered by Z-index
    /// </summary>
    public IReadOnlyList<ClockLayer> Layers => _clockLayers.OrderBy(l => l.ZIndex).ToList().AsReadOnly();

    /// <summary>
    /// Gets or sets whether advanced mode is enabled
    /// </summary>
    public bool AdvancedModeEnabled { get; set; } = false;

    /// <summary>
    /// Initializes a new instance of the <see cref="ClockLayerManager"/> class
    /// </summary>
    /// <param name="logger">Logger instance</param>
    /// <param name="coreLayerManager">Core layer manager</param>
    public ClockLayerManager(
        ILogger<ClockLayerManager> logger,
        ILayerManager coreLayerManager)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _coreLayerManager = coreLayerManager ?? throw new ArgumentNullException(nameof(coreLayerManager));

        InitializeDefaultLayers();
    }

    /// <summary>
    /// Adds a clock layer
    /// Last Updated: 2025-01-11 18:30:00 UTC
    /// </summary>
    /// <param name="layer">Layer to add</param>
    [TestableMethod("LayerManagement", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 50)]
    public void AddLayer(ClockLayer layer)
    {
        if (layer == null)
        {
            _logger.LogWarning("Attempted to add null layer");
            return;
        }

        ThrowIfDisposed();

        try
        {
            // Add to core layer manager
            _coreLayerManager.AddLayer(layer);

            // Add to clock layers collection
            _clockLayers.Add(layer);

            // Create layer data
            _layerData[layer.Id] = new ClockLayerData
            {
                Layer = layer,
                NeedsRedraw = true
            };

            _logger.LogDebug("Added clock layer: {LayerName} ({LayerType})", layer.Name, layer.LayerType);
            LayerAdded?.Invoke(layer);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding clock layer: {LayerName}", layer.Name);
        }
    }

    /// <summary>
    /// Removes a clock layer
    /// Last Updated: 2025-01-11 18:30:00 UTC
    /// </summary>
    /// <param name="layer">Layer to remove</param>
    /// <returns>True if removed successfully</returns>
    [TestableMethod("LayerManagement", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 50)]
    public bool RemoveLayer(ClockLayer layer)
    {
        if (layer == null)
        {
            _logger.LogWarning("Attempted to remove null layer");
            return false;
        }

        ThrowIfDisposed();

        try
        {
            // Remove from core layer manager
            var removed = _coreLayerManager.RemoveLayer(layer);
            if (!removed)
                return false;

            // Remove from clock layers collection
            _clockLayers.Remove(layer);

            // Clean up layer data
            if (_layerData.TryGetValue(layer.Id, out var layerData))
            {
                layerData.RenderedContent?.Dispose();
                _layerData.Remove(layer.Id);
            }

            _logger.LogDebug("Removed clock layer: {LayerName}", layer.Name);
            LayerRemoved?.Invoke(layer);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing clock layer: {LayerName}", layer.Name);
            return false;
        }
    }

    /// <summary>
    /// Gets a layer by its ID
    /// Last Updated: 2025-01-11 18:30:00 UTC
    /// </summary>
    /// <param name="layerId">Layer ID</param>
    /// <returns>Layer or null if not found</returns>
    [TestableMethod("LayerRetrieval", IncludeParameterValidation = true, ExpectedExecutionTimeMs = 10)]
    public ClockLayer? GetLayer(Guid layerId)
    {
        return _clockLayers.FirstOrDefault(l => l.Id == layerId);
    }

    /// <summary>
    /// Gets layers by type
    /// </summary>
    /// <param name="layerType">Layer type</param>
    /// <returns>Layers of the specified type</returns>
    public IEnumerable<ClockLayer> GetLayersByType(ClockLayerType layerType)
    {
        return _clockLayers.Where(l => l.LayerType == layerType);
    }

    /// <summary>
    /// Updates layer Z-index and reorders layers
    /// </summary>
    /// <param name="layer">Layer to reorder</param>
    /// <param name="newZIndex">New Z-index</param>
    public void ReorderLayer(ClockLayer layer, int newZIndex)
    {
        if (layer == null)
        {
            _logger.LogWarning("Attempted to reorder null layer");
            return;
        }

        ThrowIfDisposed();

        try
        {
            var oldZIndex = layer.ZIndex;
            layer.ZIndex = newZIndex;

            _logger.LogDebug("Reordered layer {LayerName} from Z-index {OldIndex} to {NewIndex}",
                layer.Name, oldZIndex, newZIndex);

            LayersReordered?.Invoke();
            LayerChanged?.Invoke(layer);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reordering layer: {LayerName}", layer.Name);
        }
    }

    /// <summary>
    /// Sets layer visibility
    /// </summary>
    /// <param name="layer">Layer to modify</param>
    /// <param name="isVisible">Visibility state</param>
    public void SetLayerVisibility(ClockLayer layer, bool isVisible)
    {
        if (layer == null)
        {
            _logger.LogWarning("Attempted to set visibility on null layer");
            return;
        }

        ThrowIfDisposed();

        try
        {
            layer.IsVisible = isVisible;
            MarkLayerForRedraw(layer.Id);

            _logger.LogDebug("Set layer {LayerName} visibility to {IsVisible}", layer.Name, isVisible);
            LayerChanged?.Invoke(layer);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting layer visibility: {LayerName}", layer.Name);
        }
    }

    /// <summary>
    /// Sets layer opacity
    /// </summary>
    /// <param name="layer">Layer to modify</param>
    /// <param name="opacity">Opacity value (0.0 to 1.0)</param>
    public void SetLayerOpacity(ClockLayer layer, double opacity)
    {
        if (layer == null)
        {
            _logger.LogWarning("Attempted to set opacity on null layer");
            return;
        }

        ThrowIfDisposed();

        try
        {
            layer.Opacity = Math.Clamp(opacity, 0.0, 1.0);
            MarkLayerForRedraw(layer.Id);

            _logger.LogDebug("Set layer {LayerName} opacity to {Opacity:F2}", layer.Name, opacity);
            LayerChanged?.Invoke(layer);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting layer opacity: {LayerName}", layer.Name);
        }
    }

    /// <summary>
    /// Sets layer blend mode
    /// </summary>
    /// <param name="layer">Layer to modify</param>
    /// <param name="blendMode">Blend mode</param>
    public void SetLayerBlendMode(ClockLayer layer, ClockBlendMode blendMode)
    {
        if (layer == null)
        {
            _logger.LogWarning("Attempted to set blend mode on null layer");
            return;
        }

        ThrowIfDisposed();

        try
        {
            layer.BlendMode = blendMode;
            MarkLayerForRedraw(layer.Id);

            _logger.LogDebug("Set layer {LayerName} blend mode to {BlendMode}", layer.Name, blendMode);
            LayerChanged?.Invoke(layer);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting layer blend mode: {LayerName}", layer.Name);
        }
    }

    /// <summary>
    /// Sets layer lock state
    /// </summary>
    /// <param name="layer">Layer to modify</param>
    /// <param name="isLocked">Lock state</param>
    public void SetLayerLocked(ClockLayer layer, bool isLocked)
    {
        if (layer == null)
        {
            _logger.LogWarning("Attempted to set lock state on null layer");
            return;
        }

        ThrowIfDisposed();

        try
        {
            layer.Element.IsLocked = isLocked;
            _logger.LogDebug("Set layer {LayerName} lock state to {IsLocked}", layer.Name, isLocked);
            LayerChanged?.Invoke(layer);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting layer lock state: {LayerName}", layer.Name);
        }
    }

    /// <summary>
    /// Sets layer transformation properties
    /// </summary>
    /// <param name="layer">Layer to modify</param>
    /// <param name="rotation">Rotation angle in degrees</param>
    /// <param name="scaleX">Scale factor for X axis</param>
    /// <param name="scaleY">Scale factor for Y axis</param>
    /// <param name="skewX">Skew angle for X axis in degrees</param>
    /// <param name="skewY">Skew angle for Y axis in degrees</param>
    public void SetLayerTransformation(ClockLayer layer, double rotation, double scaleX, double scaleY, double skewX = 0.0, double skewY = 0.0)
    {
        if (layer == null)
        {
            _logger.LogWarning("Attempted to set transformation on null layer");
            return;
        }

        if (layer.Element.IsLocked)
        {
            _logger.LogWarning("Attempted to transform locked layer: {LayerName}", layer.Name);
            return;
        }

        ThrowIfDisposed();

        try
        {
            layer.Element.UpdateTransformation(rotation, scaleX, scaleY, skewX, skewY);
            MarkLayerForRedraw(layer.Id);

            _logger.LogDebug("Set layer {LayerName} transformation - Rotation: {Rotation:F1}°, Scale: {ScaleX:F2}x{ScaleY:F2}, Skew: {SkewX:F1}°x{SkewY:F1}°",
                layer.Name, rotation, scaleX, scaleY, skewX, skewY);
            LayerChanged?.Invoke(layer);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting layer transformation: {LayerName}", layer.Name);
        }
    }

    /// <summary>
    /// Marks a layer for redraw
    /// </summary>
    /// <param name="layerId">Layer ID</param>
    public void MarkLayerForRedraw(Guid layerId)
    {
        if (_layerData.TryGetValue(layerId, out var layerData))
        {
            layerData.NeedsRedraw = true;
        }
    }

    /// <summary>
    /// Marks all layers for redraw
    /// </summary>
    public void MarkAllLayersForRedraw()
    {
        foreach (var layerData in _layerData.Values)
        {
            layerData.NeedsRedraw = true;
        }
    }

    /// <summary>
    /// Moves a layer to a new Z-index position
    /// </summary>
    /// <param name="layer">Layer to move</param>
    /// <param name="newZIndex">New Z-index</param>
    public void MoveLayerToZIndex(ClockLayer layer, int newZIndex)
    {
        if (layer == null)
        {
            _logger.LogWarning("Attempted to move null layer");
            return;
        }

        ThrowIfDisposed();

        try
        {
            var oldZIndex = layer.ZIndex;
            layer.ZIndex = newZIndex;
            MarkLayerForRedraw(layer.Id);

            _logger.LogDebug("Moved layer {LayerName} from Z-index {OldZIndex} to {NewZIndex}", layer.Name, oldZIndex, newZIndex);
            LayersReordered?.Invoke();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error moving layer to Z-index: {LayerName}", layer.Name);
        }
    }

    /// <summary>
    /// Moves a layer up one position in the layer order
    /// </summary>
    /// <param name="layer">Layer to move up</param>
    public void MoveLayerUp(ClockLayer layer)
    {
        if (layer == null)
        {
            _logger.LogWarning("Attempted to move up null layer");
            return;
        }

        ThrowIfDisposed();

        try
        {
            var layers = Layers.ToList();
            var currentIndex = layers.FindIndex(l => l.Id == layer.Id);

            if (currentIndex < layers.Count - 1)
            {
                var nextLayer = layers[currentIndex + 1];
                var tempZIndex = layer.ZIndex;
                layer.ZIndex = nextLayer.ZIndex;
                nextLayer.ZIndex = tempZIndex;

                MarkLayerForRedraw(layer.Id);
                MarkLayerForRedraw(nextLayer.Id);

                _logger.LogDebug("Moved layer {LayerName} up in order", layer.Name);
                LayersReordered?.Invoke();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error moving layer up: {LayerName}", layer.Name);
        }
    }

    /// <summary>
    /// Moves a layer down one position in the layer order
    /// </summary>
    /// <param name="layer">Layer to move down</param>
    public void MoveLayerDown(ClockLayer layer)
    {
        if (layer == null)
        {
            _logger.LogWarning("Attempted to move down null layer");
            return;
        }

        ThrowIfDisposed();

        try
        {
            var layers = Layers.ToList();
            var currentIndex = layers.FindIndex(l => l.Id == layer.Id);

            if (currentIndex > 0)
            {
                var previousLayer = layers[currentIndex - 1];
                var tempZIndex = layer.ZIndex;
                layer.ZIndex = previousLayer.ZIndex;
                previousLayer.ZIndex = tempZIndex;

                MarkLayerForRedraw(layer.Id);
                MarkLayerForRedraw(previousLayer.Id);

                _logger.LogDebug("Moved layer {LayerName} down in order", layer.Name);
                LayersReordered?.Invoke();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error moving layer down: {LayerName}", layer.Name);
        }
    }

    /// <summary>
    /// Moves a layer to the top of the layer order
    /// </summary>
    /// <param name="layer">Layer to move to top</param>
    public void MoveLayerToTop(ClockLayer layer)
    {
        if (layer == null)
        {
            _logger.LogWarning("Attempted to move to top null layer");
            return;
        }

        ThrowIfDisposed();

        try
        {
            var maxZIndex = Layers.Max(l => l.ZIndex);
            MoveLayerToZIndex(layer, maxZIndex + 10);
            _logger.LogDebug("Moved layer {LayerName} to top", layer.Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error moving layer to top: {LayerName}", layer.Name);
        }
    }

    /// <summary>
    /// Moves a layer to the bottom of the layer order
    /// </summary>
    /// <param name="layer">Layer to move to bottom</param>
    public void MoveLayerToBottom(ClockLayer layer)
    {
        if (layer == null)
        {
            _logger.LogWarning("Attempted to move to bottom null layer");
            return;
        }

        ThrowIfDisposed();

        try
        {
            var minZIndex = Layers.Min(l => l.ZIndex);
            MoveLayerToZIndex(layer, minZIndex - 10);
            _logger.LogDebug("Moved layer {LayerName} to bottom", layer.Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error moving layer to bottom: {LayerName}", layer.Name);
        }
    }

    /// <summary>
    /// Clears all layers
    /// </summary>
    public void ClearLayers()
    {
        ThrowIfDisposed();

        try
        {
            // Dispose rendered content
            foreach (var layerData in _layerData.Values)
            {
                layerData.RenderedContent?.Dispose();
            }

            _layerData.Clear();
            _clockLayers.Clear();
            _coreLayerManager.ClearLayers();

            _logger.LogDebug("Cleared all clock layers");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing layers");
        }
    }

    /// <summary>
    /// Initializes default layers for clock components
    /// </summary>
    private void InitializeDefaultLayers()
    {
        try
        {
            // Create default layers in proper order
            var backgroundLayer = new ClockLayer("Background", ClockLayerType.Background) { ZIndex = 0 };
            var timeLayer = new ClockLayer("Time", ClockLayerType.Time) { ZIndex = 10 };
            var dateLayer = new ClockLayer("Date", ClockLayerType.Date) { ZIndex = 20 };
            var amPmLayer = new ClockLayer("AM/PM", ClockLayerType.AmPm) { ZIndex = 30 };
            var effectsLayer = new ClockLayer("Effects", ClockLayerType.Effects) { ZIndex = 40 };

            AddLayer(backgroundLayer);
            AddLayer(timeLayer);
            AddLayer(dateLayer);
            AddLayer(amPmLayer);
            AddLayer(effectsLayer);

            _logger.LogDebug("Initialized {Count} default clock layers", 5);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error initializing default layers");
        }
    }

    /// <summary>
    /// Throws if the manager has been disposed
    /// </summary>
    private void ThrowIfDisposed()
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(ClockLayerManager));
    }

    /// <summary>
    /// Renders all visible layers to a canvas with proper blending
    /// </summary>
    /// <param name="canvas">Canvas to render to</param>
    /// <param name="width">Canvas width</param>
    /// <param name="height">Canvas height</param>
    /// <param name="renderContext">Rendering context data</param>
    public void RenderLayers(SKCanvas canvas, int width, int height, object? renderContext = null)
    {
        if (canvas == null)
        {
            _logger.LogWarning("Cannot render layers to null canvas");
            return;
        }

        ThrowIfDisposed();

        try
        {
            // Get visible layers ordered by Z-index
            var visibleLayers = Layers.Where(l => l.IsVisible).OrderBy(l => l.ZIndex).ToList();

            _logger.LogDebug("Rendering {Count} visible layers", visibleLayers.Count);

            foreach (var layer in visibleLayers)
            {
                RenderLayer(canvas, layer, width, height, renderContext);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rendering layers");
        }
    }

    /// <summary>
    /// Renders a single layer with blend mode and opacity
    /// </summary>
    /// <param name="canvas">Canvas to render to</param>
    /// <param name="layer">Layer to render</param>
    /// <param name="width">Canvas width</param>
    /// <param name="height">Canvas height</param>
    /// <param name="renderContext">Rendering context data</param>
    private void RenderLayer(SKCanvas canvas, ClockLayer layer, int width, int height, object? renderContext)
    {
        try
        {
            if (!_layerData.TryGetValue(layer.Id, out var layerData))
            {
                _logger.LogWarning("No layer data found for layer: {LayerName}", layer.Name);
                return;
            }

            // Create layer surface for isolated rendering
            using var layerSurface = SKSurface.Create(new SKImageInfo(width, height));
            var layerCanvas = layerSurface.Canvas;

            // Render layer content based on type
            RenderLayerContent(layerCanvas, layer, width, height, renderContext);

            // Get the rendered layer as an image
            using var layerImage = layerSurface.Snapshot();
            using var layerBitmap = SKBitmap.FromImage(layerImage);

            // Apply blend mode and opacity
            ApplyLayerBlending(canvas, layerBitmap, layer);

            _logger.LogDebug("Rendered layer: {LayerName} with blend mode {BlendMode} and opacity {Opacity:F2}",
                layer.Name, layer.BlendMode, layer.Opacity);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rendering layer: {LayerName}", layer.Name);
        }
    }

    /// <summary>
    /// Renders the actual content of a layer based on its type
    /// </summary>
    /// <param name="canvas">Canvas to render to</param>
    /// <param name="layer">Layer to render</param>
    /// <param name="width">Canvas width</param>
    /// <param name="height">Canvas height</param>
    /// <param name="renderContext">Rendering context data</param>
    private void RenderLayerContent(SKCanvas canvas, ClockLayer layer, int width, int height, object? renderContext)
    {
        // This method will be called by the ClockWorkshopViewModel
        // to render specific layer content. For now, it's a placeholder
        // that will be integrated with the existing rendering pipeline.

        switch (layer.LayerType)
        {
            case ClockLayerType.Background:
                // Background rendering will be handled by existing RenderBackgroundShape method
                break;
            case ClockLayerType.Time:
                // Time rendering will be handled by existing time rendering code
                break;
            case ClockLayerType.Date:
                // Date rendering will be handled by DateComponentManager
                break;
            case ClockLayerType.AmPm:
                // AM/PM rendering will be handled by existing AM/PM code
                break;
            case ClockLayerType.Effects:
                // Effects rendering for glow, shadow, etc.
                break;
        }
    }

    /// <summary>
    /// Applies blend mode and opacity to a layer
    /// </summary>
    /// <param name="canvas">Target canvas</param>
    /// <param name="layerBitmap">Layer bitmap to blend</param>
    /// <param name="layer">Layer with blend settings</param>
    private void ApplyLayerBlending(SKCanvas canvas, SKBitmap layerBitmap, ClockLayer layer)
    {
        try
        {
            // Use SkiaSharp blend modes directly for now
            using var paint = new SKPaint
            {
                Color = SKColors.White.WithAlpha((byte)(255 * layer.Opacity)),
                BlendMode = ConvertToSKBlendMode(layer.BlendMode)
            };

            canvas.DrawBitmap(layerBitmap, 0, 0, paint);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying layer blending for layer: {LayerName}", layer.Name);

            // Fallback: draw without blending
            using var fallbackPaint = new SKPaint
            {
                Color = SKColors.White.WithAlpha((byte)(255 * layer.Opacity))
            };
            canvas.DrawBitmap(layerBitmap, 0, 0, fallbackPaint);
        }
    }

    /// <summary>
    /// Converts ClockBlendMode to SKBlendMode for SkiaSharp
    /// </summary>
    /// <param name="clockBlendMode">Clock blend mode</param>
    /// <returns>SkiaSharp blend mode</returns>
    private SKBlendMode ConvertToSKBlendMode(ClockBlendMode clockBlendMode)
    {
        return clockBlendMode switch
        {
            ClockBlendMode.Normal => SKBlendMode.SrcOver,
            ClockBlendMode.Multiply => SKBlendMode.Multiply,
            ClockBlendMode.Screen => SKBlendMode.Screen,
            ClockBlendMode.Overlay => SKBlendMode.Overlay,
            ClockBlendMode.SoftLight => SKBlendMode.SoftLight,
            ClockBlendMode.HardLight => SKBlendMode.HardLight,
            ClockBlendMode.ColorDodge => SKBlendMode.ColorDodge,
            ClockBlendMode.ColorBurn => SKBlendMode.ColorBurn,
            ClockBlendMode.Darken => SKBlendMode.Darken,
            ClockBlendMode.Lighten => SKBlendMode.Lighten,
            ClockBlendMode.Difference => SKBlendMode.Difference,
            ClockBlendMode.Exclusion => SKBlendMode.Exclusion,
            _ => SKBlendMode.SrcOver
        };
    }

    /// <summary>
    /// Disposes the layer manager
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        try
        {
            ClearLayers();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during layer manager disposal");
        }
        finally
        {
            _disposed = true;
        }
    }
}
