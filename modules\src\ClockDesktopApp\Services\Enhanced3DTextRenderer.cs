using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using ArtDesignFramework.ClockDesktopApp.Models;
using ArtDesignFramework.Core;
using Microsoft.Extensions.Logging;
using SkiaSharp;

namespace ArtDesignFramework.ClockDesktopApp.Services
{
    /// <summary>
    /// Enhanced 3D text renderer that leverages the framework's Text3D module
    /// for advanced lighting, materials, and effects
    /// </summary>
    public interface IEnhanced3DTextRenderer
    {
        /// <summary>
        /// Renders 3D text with enhanced lighting and materials
        /// </summary>
        Task<SKBitmap> RenderEnhanced3DTextAsync(
            string text,
            ClockSettings settings,
            SKPaint basePaint,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Renders 3D text with opacity support
        /// </summary>
        Task<SKBitmap> RenderEnhanced3DTextWithOpacityAsync(
            string text,
            ClockSettings settings,
            SKPaint basePaint,
            double opacity,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Creates a preview of the 3D text for real-time updates
        /// </summary>
        Task<SKBitmap> RenderPreviewAsync(
            string text,
            ClockSettings settings,
            SKPaint basePaint,
            CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Implementation of enhanced 3D text renderer that provides advanced lighting and materials
    /// without external dependencies
    /// </summary>
    [Testable]
    public class Enhanced3DTextRenderer : IEnhanced3DTextRenderer
    {
        private readonly ILogger<Enhanced3DTextRenderer> _logger;
        private readonly Dictionary<string, SKBitmap> _renderCache;
        private readonly object _cacheLock = new object();

        public Enhanced3DTextRenderer(ILogger<Enhanced3DTextRenderer> logger)
        {
            _logger = logger;
            _renderCache = new Dictionary<string, SKBitmap>();
        }

        /// <inheritdoc />
        /// <summary>
        /// Renders 3D text with enhanced lighting and materials
        /// Last Updated: 2025-01-11 18:30:00 UTC
        /// </summary>
        [TestableMethod("Enhanced3DRendering", IncludePerformanceTests = true, IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 500)]
        public async Task<SKBitmap> RenderEnhanced3DTextAsync(
            string text,
            ClockSettings settings,
            SKPaint basePaint,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var cacheKey = GenerateCacheKey(text, settings, 1.0);

                lock (_cacheLock)
                {
                    if (_renderCache.TryGetValue(cacheKey, out var cachedBitmap))
                    {
                        return cachedBitmap.Copy();
                    }
                }

                // Use enhanced 3D rendering with advanced lighting and materials
                var result = await Task.Run(() => RenderEnhanced3DTextInternal(text, settings, basePaint));

                // Cache the result
                lock (_cacheLock)
                {
                    if (_renderCache.Count > 50) // Limit cache size
                    {
                        var oldestKey = _renderCache.Keys.First();
                        _renderCache[oldestKey]?.Dispose();
                        _renderCache.Remove(oldestKey);
                    }
                    _renderCache[cacheKey] = result.Copy();
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rendering enhanced 3D text: {Text}", text);
                // Fall back to basic rendering
                return await Task.Run(() => RenderBasic3DText(text, settings, basePaint));
            }
        }

        /// <inheritdoc />
        /// <summary>
        /// Renders 3D text with opacity support
        /// Last Updated: 2025-01-11 18:30:00 UTC
        /// </summary>
        [TestableMethod("Enhanced3DRenderingWithOpacity", IncludePerformanceTests = true, IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 600)]
        public async Task<SKBitmap> RenderEnhanced3DTextWithOpacityAsync(
            string text,
            ClockSettings settings,
            SKPaint basePaint,
            double opacity,
            CancellationToken cancellationToken = default)
        {
            var result = await RenderEnhanced3DTextAsync(text, settings, basePaint, cancellationToken);

            if (Math.Abs(opacity - 1.0) > 0.001)
            {
                // Apply opacity to the entire rendered bitmap
                result = ApplyOpacityToBitmap(result, opacity);
            }

            return result;
        }

        /// <inheritdoc />
        /// <summary>
        /// Creates a preview of the 3D text for real-time updates
        /// Last Updated: 2025-01-11 18:30:00 UTC
        /// </summary>
        [TestableMethod("Enhanced3DPreview", IncludePerformanceTests = true, IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 300)]
        public async Task<SKBitmap> RenderPreviewAsync(
            string text,
            ClockSettings settings,
            SKPaint basePaint,
            CancellationToken cancellationToken = default)
        {
            // For preview, use lower quality settings for better performance
            var previewSettings = CreatePreviewSettings(settings);
            return await RenderEnhanced3DTextAsync(text, previewSettings, basePaint, cancellationToken);
        }

        /// <summary>
        /// Enhanced 3D text rendering with advanced lighting and materials
        /// </summary>
        private SKBitmap RenderEnhanced3DTextInternal(string text, ClockSettings settings, SKPaint basePaint)
        {
            // Calculate text bounds for proper sizing
            var textBounds = new SKRect();
            basePaint.MeasureText(text, ref textBounds);

            var width = (int)Math.Max(textBounds.Width + 100, 400);
            var height = (int)Math.Max(textBounds.Height + 100, 200);

            var bitmap = new SKBitmap(width, height);
            using var canvas = new SKCanvas(bitmap);
            canvas.Clear(SKColors.Transparent);

            // Calculate text position
            var x = (width - textBounds.Width) / 2 - textBounds.Left;
            var y = (height - textBounds.Height) / 2 - textBounds.Top;

            // Render enhanced 3D text with advanced features
            RenderEnhanced3DLayers(canvas, text, x, y, settings, basePaint);

            return bitmap;
        }

        /// <summary>
        /// Renders 3D text with enhanced lighting, materials, and effects
        /// </summary>
        private void RenderEnhanced3DLayers(SKCanvas canvas, string text, float x, float y, ClockSettings settings, SKPaint basePaint)
        {
            var depth = (float)settings.TextDepth;
            var bevel = (float)settings.BevelSize;

            // Enhanced lighting calculations
            var lightingData = CalculateEnhancedLighting(settings);

            // Render depth layers with enhanced lighting
            for (int i = (int)depth; i >= 0; i--)
            {
                using var layerPaint = basePaint.Clone();

                // Calculate enhanced lighting for this layer
                var lightingFactor = CalculateLayerLighting(i, depth, lightingData, settings);
                var materialColor = ApplyMaterialProperties(ToSKColor(settings.Text3DColor), settings.Material, lightingFactor);

                layerPaint.Color = materialColor;

                // Apply enhanced effects
                if (settings.EnableAmbientOcclusion && i > depth * 0.7f)
                {
                    var occlusionFactor = CalculateAmbientOcclusion(i, depth, settings);
                    layerPaint.Color = BlendWithAmbientOcclusion(layerPaint.Color, settings, occlusionFactor);
                }

                // Enhanced shadow rendering
                if (settings.EnableSoftShadows && i == depth)
                {
                    RenderSoftShadow(canvas, text, x + i * 0.5f, y + i * 0.5f, layerPaint, settings);
                }

                // Draw the layer with offset
                var offsetX = x + (i * 0.5f);
                var offsetY = y + (i * 0.5f);

                // Add enhanced bevel effect
                if (i <= bevel)
                {
                    ApplyEnhancedBevel(layerPaint, i, bevel, settings);
                }

                canvas.DrawText(text, offsetX, offsetY, layerPaint);
            }

            // Render front face with environment reflection if enabled
            using var frontPaint = basePaint.Clone();
            var frontColor = ApplyMaterialProperties(ToSKColor(settings.Text3DColor), settings.Material, lightingData.PrimaryIntensity);

            if (settings.EnableEnvironmentReflection)
            {
                frontColor = ApplyEnvironmentReflection(frontColor, settings);
            }

            frontPaint.Color = frontColor;
            canvas.DrawText(text, x, y, frontPaint);
        }

        /// <summary>
        /// Enhanced lighting data structure supporting up to 4 light sources with advanced properties
        /// </summary>
        private class EnhancedLightingData
        {
            public float PrimaryIntensity { get; set; }
            public float SecondaryIntensity { get; set; }
            public float TertiaryIntensity { get; set; }
            public float QuaternaryIntensity { get; set; }
            public float AmbientIntensity { get; set; }

            public SKColor PrimaryColor { get; set; }
            public SKColor SecondaryColor { get; set; }
            public SKColor TertiaryColor { get; set; }
            public SKColor QuaternaryColor { get; set; }
            public SKColor AmbientColor { get; set; }

            public float[] LightDirectionX { get; set; } = new float[4];
            public float[] LightDirectionY { get; set; } = new float[4];
            public LightType[] LightTypes { get; set; } = new LightType[4];
            public bool[] LightEnabled { get; set; } = new bool[4];

            // Advanced light properties
            public LightPriority[] LightPriorities { get; set; } = new LightPriority[4];
            public float SpotLightConeAngle { get; set; }
            public float SpotLightPenumbraAngle { get; set; }
            public float AreaLightWidth { get; set; }
            public float AreaLightHeight { get; set; }
            public bool EnableAdvancedFalloff { get; set; }
        }

        /// <summary>
        /// Calculate enhanced lighting data from settings with support for 4 light sources
        /// </summary>
        private EnhancedLightingData CalculateEnhancedLighting(ClockSettings settings)
        {
            var lightingData = new EnhancedLightingData
            {
                // Primary light (always enabled when lighting is on)
                PrimaryIntensity = (float)settings.LightIntensity,
                PrimaryColor = ToSKColor(settings.LightColor),
                AmbientIntensity = (float)settings.AmbientIntensity,
                AmbientColor = settings.AmbientColor != null ? ToSKColor(settings.AmbientColor) : SKColors.Gray
            };

            // Primary light setup
            lightingData.LightDirectionX[0] = (float)Math.Cos(settings.LightAngleX * Math.PI / 180.0);
            lightingData.LightDirectionY[0] = (float)Math.Sin(settings.LightAngleY * Math.PI / 180.0);
            lightingData.LightTypes[0] = LightType.Directional; // Primary is always directional
            lightingData.LightEnabled[0] = settings.EnableLighting;

            // Secondary light setup
            if (settings.EnableMultipleLights)
            {
                lightingData.SecondaryIntensity = (float)settings.SecondaryLightIntensity;
                lightingData.SecondaryColor = ToSKColor(settings.SecondaryLightColor);
                lightingData.LightDirectionX[1] = (float)Math.Cos(settings.SecondaryLightAngleX * Math.PI / 180.0);
                lightingData.LightDirectionY[1] = (float)Math.Sin(settings.SecondaryLightAngleY * Math.PI / 180.0);
                lightingData.LightTypes[1] = settings.SecondaryLightType;
                lightingData.LightEnabled[1] = true;
            }

            // Tertiary light setup
            if (settings.EnableTertiaryLight)
            {
                lightingData.TertiaryIntensity = (float)settings.TertiaryLightIntensity;
                lightingData.TertiaryColor = ToSKColor(settings.TertiaryLightColor);
                lightingData.LightDirectionX[2] = (float)Math.Cos(settings.TertiaryLightAngleX * Math.PI / 180.0);
                lightingData.LightDirectionY[2] = (float)Math.Sin(settings.TertiaryLightAngleY * Math.PI / 180.0);
                lightingData.LightTypes[2] = settings.TertiaryLightType;
                lightingData.LightEnabled[2] = true;
            }

            // Quaternary light setup
            if (settings.EnableQuaternaryLight)
            {
                lightingData.QuaternaryIntensity = (float)settings.QuaternaryLightIntensity;
                lightingData.QuaternaryColor = ToSKColor(settings.QuaternaryLightColor);
                lightingData.LightDirectionX[3] = (float)Math.Cos(settings.QuaternaryLightAngleX * Math.PI / 180.0);
                lightingData.LightDirectionY[3] = (float)Math.Sin(settings.QuaternaryLightAngleY * Math.PI / 180.0);
                lightingData.LightTypes[3] = settings.QuaternaryLightType;
                lightingData.LightEnabled[3] = true;
            }

            // Set light priorities
            lightingData.LightPriorities[0] = settings.PrimaryLightPriority;
            lightingData.LightPriorities[1] = settings.SecondaryLightPriority;
            lightingData.LightPriorities[2] = settings.TertiaryLightPriority;
            lightingData.LightPriorities[3] = settings.QuaternaryLightPriority;

            // Set advanced light properties
            lightingData.SpotLightConeAngle = (float)settings.SpotLightConeAngle;
            lightingData.SpotLightPenumbraAngle = (float)settings.SpotLightPenumbraAngle;
            lightingData.AreaLightWidth = (float)settings.AreaLightWidth;
            lightingData.AreaLightHeight = (float)settings.AreaLightHeight;
            lightingData.EnableAdvancedFalloff = settings.EnableAdvancedLightFalloff;

            return lightingData;
        }

        /// <summary>
        /// Calculate lighting for a specific layer with support for 4 light sources and priority system
        /// </summary>
        private float CalculateLayerLighting(int layer, float totalDepth, EnhancedLightingData lightingData, ClockSettings settings)
        {
            var depthRatio = layer / totalDepth;
            var baseLighting = lightingData.AmbientIntensity;

            // Apply light priority system if enabled
            if (settings.EnableLightPriority)
            {
                baseLighting += CalculatePrioritizedLighting(depthRatio, lightingData, settings);
            }
            else
            {
                // Standard lighting calculation without priority
                baseLighting += CalculateStandardLighting(depthRatio, lightingData);
            }

            // Apply advanced light blending mode
            baseLighting = ApplyAdvancedLightBlending(baseLighting, settings);

            // Global illumination simulation
            if (settings.EnableGlobalIllumination)
            {
                var bounceContribution = CalculateGlobalIllumination(depthRatio, settings);
                baseLighting += bounceContribution;
            }

            return Math.Max(0.1f, Math.Min(2.0f, baseLighting));
        }

        /// <summary>
        /// Calculate lighting with priority system for performance optimization
        /// </summary>
        private float CalculatePrioritizedLighting(float depthRatio, EnhancedLightingData lightingData, ClockSettings settings)
        {
            var totalLighting = 0f;
            var lightBudget = 1.0f; // Available lighting budget for performance

            // Create priority-ordered light list
            var prioritizedLights = new[]
            {
                new { Index = 0, Priority = settings.PrimaryLightPriority, Enabled = lightingData.LightEnabled[0],
                      Intensity = lightingData.PrimaryIntensity, Type = lightingData.LightTypes[0], Falloff = 0.6f },
                new { Index = 1, Priority = settings.SecondaryLightPriority, Enabled = lightingData.LightEnabled[1],
                      Intensity = lightingData.SecondaryIntensity, Type = lightingData.LightTypes[1], Falloff = 0.4f },
                new { Index = 2, Priority = settings.TertiaryLightPriority, Enabled = lightingData.LightEnabled[2],
                      Intensity = lightingData.TertiaryIntensity, Type = lightingData.LightTypes[2], Falloff = 0.3f },
                new { Index = 3, Priority = settings.QuaternaryLightPriority, Enabled = lightingData.LightEnabled[3],
                      Intensity = lightingData.QuaternaryIntensity, Type = lightingData.LightTypes[3], Falloff = 0.2f }
            }
            .Where(light => light.Enabled)
            .OrderByDescending(light => (int)light.Priority)
            .ToArray();

            // Process lights in priority order
            foreach (var light in prioritizedLights)
            {
                if (lightBudget <= 0) break; // Performance optimization: skip low-priority lights if budget exhausted

                var contribution = CalculateLightContribution(light.Intensity, light.Type, depthRatio, light.Falloff);

                // Apply priority-based scaling
                var priorityScale = GetPriorityScale(light.Priority);
                contribution *= priorityScale;

                // Consume lighting budget based on priority
                var budgetCost = GetBudgetCost(light.Priority);
                lightBudget -= budgetCost;

                totalLighting += contribution;
            }

            return totalLighting;
        }

        /// <summary>
        /// Calculate standard lighting without priority system
        /// </summary>
        private float CalculateStandardLighting(float depthRatio, EnhancedLightingData lightingData)
        {
            var totalLighting = 0f;

            // Primary light contribution
            if (lightingData.LightEnabled[0])
            {
                totalLighting += CalculateLightContribution(
                    lightingData.PrimaryIntensity, lightingData.LightTypes[0], depthRatio, 0.6f);
            }

            // Secondary light contribution
            if (lightingData.LightEnabled[1])
            {
                totalLighting += CalculateLightContribution(
                    lightingData.SecondaryIntensity, lightingData.LightTypes[1], depthRatio, 0.4f);
            }

            // Tertiary light contribution
            if (lightingData.LightEnabled[2])
            {
                totalLighting += CalculateLightContribution(
                    lightingData.TertiaryIntensity, lightingData.LightTypes[2], depthRatio, 0.3f);
            }

            // Quaternary light contribution
            if (lightingData.LightEnabled[3])
            {
                totalLighting += CalculateLightContribution(
                    lightingData.QuaternaryIntensity, lightingData.LightTypes[3], depthRatio, 0.2f);
            }

            return totalLighting;
        }

        /// <summary>
        /// Get priority-based scaling factor
        /// </summary>
        private float GetPriorityScale(LightPriority priority)
        {
            return priority switch
            {
                LightPriority.Critical => 1.0f,
                LightPriority.High => 0.9f,
                LightPriority.Normal => 0.8f,
                LightPriority.Low => 0.6f,
                _ => 0.8f
            };
        }

        /// <summary>
        /// Get budget cost for light priority
        /// </summary>
        private float GetBudgetCost(LightPriority priority)
        {
            return priority switch
            {
                LightPriority.Critical => 0.1f, // Low cost for critical lights
                LightPriority.High => 0.2f,
                LightPriority.Normal => 0.3f,
                LightPriority.Low => 0.4f, // High cost for low-priority lights
                _ => 0.3f
            };
        }

        /// <summary>
        /// Apply material properties to color
        /// </summary>
        private SKColor ApplyMaterialProperties(SKColor baseColor, SurfaceMaterial material, float lightingFactor)
        {
            var materialData = GetMaterialData(material);

            // Apply metallic properties
            if (materialData.Metallic > 0.5f)
            {
                lightingFactor *= 1.0f + materialData.Metallic * 0.5f;
            }

            // Apply roughness
            var roughnessEffect = 1.0f - materialData.Roughness * 0.3f;
            lightingFactor *= roughnessEffect;

            // Apply reflectance
            if (materialData.Reflectance > 0.5f)
            {
                lightingFactor *= 1.0f + (materialData.Reflectance - 0.5f) * 0.4f;
            }

            // Clamp lighting factor
            lightingFactor = Math.Max(0.1f, Math.Min(2.0f, lightingFactor));

            return new SKColor(
                (byte)(baseColor.Red * lightingFactor),
                (byte)(baseColor.Green * lightingFactor),
                (byte)(baseColor.Blue * lightingFactor),
                baseColor.Alpha);
        }

        /// <summary>
        /// Material data structure
        /// </summary>
        private class MaterialData
        {
            public float Metallic { get; set; }
            public float Roughness { get; set; }
            public float Reflectance { get; set; }
            public SKColor TintColor { get; set; } = SKColors.White;
        }

        /// <summary>
        /// Get material properties
        /// </summary>
        private MaterialData GetMaterialData(SurfaceMaterial material)
        {
            return material switch
            {
                SurfaceMaterial.Chrome => new MaterialData { Metallic = 1.0f, Roughness = 0.1f, Reflectance = 0.95f, TintColor = SKColors.Silver },
                SurfaceMaterial.Gold => new MaterialData { Metallic = 1.0f, Roughness = 0.2f, Reflectance = 0.9f, TintColor = SKColors.Gold },
                SurfaceMaterial.Copper => new MaterialData { Metallic = 1.0f, Roughness = 0.3f, Reflectance = 0.8f, TintColor = SKColor.Parse("#B87333") },
                SurfaceMaterial.Glass => new MaterialData { Metallic = 0.0f, Roughness = 0.0f, Reflectance = 0.95f },
                SurfaceMaterial.Plastic => new MaterialData { Metallic = 0.0f, Roughness = 0.4f, Reflectance = 0.5f },
                SurfaceMaterial.Rubber => new MaterialData { Metallic = 0.0f, Roughness = 0.9f, Reflectance = 0.1f },
                SurfaceMaterial.Metallic => new MaterialData { Metallic = 0.8f, Roughness = 0.3f, Reflectance = 0.7f },
                SurfaceMaterial.Glossy => new MaterialData { Metallic = 0.1f, Roughness = 0.1f, Reflectance = 0.8f },
                _ => new MaterialData { Metallic = 0.0f, Roughness = 0.9f, Reflectance = 0.04f } // Matte
            };
        }

        /// <summary>
        /// Calculate ambient occlusion for enhanced depth perception
        /// </summary>
        private float CalculateAmbientOcclusion(int layer, float totalDepth, ClockSettings settings)
        {
            var depthRatio = layer / totalDepth;
            var occlusionAmount = depthRatio * settings.AmbientOcclusionIntensity;
            return (float)(1.0 - occlusionAmount);
        }

        /// <summary>
        /// Blend color with ambient occlusion
        /// </summary>
        private SKColor BlendWithAmbientOcclusion(SKColor baseColor, ClockSettings settings, float occlusionFactor)
        {
            var ambientColor = settings.AmbientColor != null ? ToSKColor(settings.AmbientColor) : SKColors.Gray;

            return new SKColor(
                (byte)(baseColor.Red * occlusionFactor + ambientColor.Red * (1 - occlusionFactor) * 0.3f),
                (byte)(baseColor.Green * occlusionFactor + ambientColor.Green * (1 - occlusionFactor) * 0.3f),
                (byte)(baseColor.Blue * occlusionFactor + ambientColor.Blue * (1 - occlusionFactor) * 0.3f),
                baseColor.Alpha);
        }

        /// <summary>
        /// Render soft shadows for enhanced realism
        /// </summary>
        private void RenderSoftShadow(SKCanvas canvas, string text, float x, float y, SKPaint paint, ClockSettings settings)
        {
            if (!settings.EnableSoftShadows) return;

            using var shadowPaint = paint.Clone();
            shadowPaint.Color = shadowPaint.Color.WithAlpha((byte)(shadowPaint.Color.Alpha * 0.3f));

            // Create soft shadow with multiple passes
            var softness = (float)settings.ShadowSoftness;
            for (int i = 0; i < 3; i++)
            {
                var blur = softness * (i + 1);
                shadowPaint.MaskFilter = SKMaskFilter.CreateBlur(SKBlurStyle.Normal, blur);
                canvas.DrawText(text, x + blur * 0.5f, y + blur * 0.5f, shadowPaint);
            }
        }

        /// <summary>
        /// Apply enhanced bevel effects
        /// </summary>
        private void ApplyEnhancedBevel(SKPaint paint, int layer, float bevelSize, ClockSettings settings)
        {
            var bevelFactor = 1.0f + (bevelSize - layer) * 0.2f;
            var currentAlpha = paint.Color.Alpha;
            paint.Color = paint.Color.WithAlpha((byte)(currentAlpha * bevelFactor));

            // Add slight highlight for bevel effect
            if (layer == 0) // Front face
            {
                var highlightAmount = 0.1f;
                paint.Color = new SKColor(
                    (byte)Math.Min(255, paint.Color.Red + 255 * highlightAmount),
                    (byte)Math.Min(255, paint.Color.Green + 255 * highlightAmount),
                    (byte)Math.Min(255, paint.Color.Blue + 255 * highlightAmount),
                    paint.Color.Alpha);
            }
        }

        /// <summary>
        /// Apply environment reflection effects
        /// </summary>
        private SKColor ApplyEnvironmentReflection(SKColor baseColor, ClockSettings settings)
        {
            if (!settings.EnableEnvironmentReflection) return baseColor;

            var reflectionIntensity = (float)settings.ReflectionIntensity;
            var environmentTint = SKColors.LightBlue; // Simulated environment color

            return new SKColor(
                (byte)(baseColor.Red * (1 - reflectionIntensity * 0.3f) + environmentTint.Red * reflectionIntensity * 0.3f),
                (byte)(baseColor.Green * (1 - reflectionIntensity * 0.3f) + environmentTint.Green * reflectionIntensity * 0.3f),
                (byte)(baseColor.Blue * (1 - reflectionIntensity * 0.3f) + environmentTint.Blue * reflectionIntensity * 0.3f),
                baseColor.Alpha);
        }

        /// <summary>
        /// Calculate individual light contribution based on light type and depth with advanced falloff
        /// </summary>
        private float CalculateLightContribution(float intensity, LightType lightType, float depthRatio, float falloffFactor)
        {
            var baseContribution = intensity * (1.0f - depthRatio * falloffFactor);

            return lightType switch
            {
                LightType.Directional => CalculateDirectionalLight(baseContribution, depthRatio),
                LightType.Point => CalculatePointLight(baseContribution, depthRatio),
                LightType.Spot => CalculateSpotLight(baseContribution, depthRatio),
                LightType.Area => CalculateAreaLight(baseContribution, depthRatio),
                _ => baseContribution
            };
        }

        /// <summary>
        /// Calculate individual light contribution with advanced properties
        /// </summary>
        private float CalculateLightContribution(float intensity, LightType lightType, float depthRatio, float falloffFactor, EnhancedLightingData lightingData)
        {
            var baseContribution = intensity * (1.0f - depthRatio * falloffFactor);

            return lightType switch
            {
                LightType.Directional => CalculateDirectionalLight(baseContribution, depthRatio),
                LightType.Point => CalculatePointLight(baseContribution, depthRatio),
                LightType.Spot => CalculateAdvancedSpotLight(baseContribution, depthRatio, lightingData),
                LightType.Area => CalculateAdvancedAreaLight(baseContribution, depthRatio, lightingData),
                _ => baseContribution
            };
        }

        /// <summary>
        /// Calculate directional light contribution (like sunlight)
        /// </summary>
        private float CalculateDirectionalLight(float baseContribution, float depthRatio)
        {
            // Directional lights have no distance falloff but can have depth-based intensity variation
            return baseContribution * (1.0f - depthRatio * 0.05f); // Minimal depth variation
        }

        /// <summary>
        /// Calculate point light contribution with inverse square falloff
        /// </summary>
        private float CalculatePointLight(float baseContribution, float depthRatio)
        {
            // Point lights have quadratic falloff with distance
            var distanceFactor = 1.0f / (1.0f + depthRatio * depthRatio * 0.5f);
            return baseContribution * distanceFactor;
        }

        /// <summary>
        /// Calculate spot light contribution with cone angle and penumbra
        /// </summary>
        private float CalculateSpotLight(float baseContribution, float depthRatio)
        {
            // Spot lights have focused beam with edge falloff
            var coneEffect = 1.0f - Math.Abs(depthRatio - 0.5f) * 0.8f; // Peak at center depth
            var distanceFactor = 1.0f / (1.0f + depthRatio * 0.4f);
            return baseContribution * coneEffect * distanceFactor;
        }

        /// <summary>
        /// Calculate area light contribution with soft falloff
        /// </summary>
        private float CalculateAreaLight(float baseContribution, float depthRatio)
        {
            // Area lights provide soft, even illumination
            var softFalloff = 1.0f - depthRatio * 0.15f; // Very gentle falloff
            return baseContribution * Math.Max(0.3f, softFalloff); // Maintain minimum illumination
        }

        /// <summary>
        /// Calculate advanced spot light contribution with cone angle and penumbra
        /// </summary>
        private float CalculateAdvancedSpotLight(float baseContribution, float depthRatio, EnhancedLightingData lightingData)
        {
            // Calculate cone effect based on settings
            var coneAngle = lightingData.SpotLightConeAngle;
            var penumbraAngle = lightingData.SpotLightPenumbraAngle;

            // Simulate cone effect - peak at center, falloff at edges
            var coneCenter = 0.5f; // Center depth
            var distanceFromCenter = Math.Abs(depthRatio - coneCenter);
            var normalizedDistance = distanceFromCenter / 0.5f; // Normalize to 0-1

            // Calculate cone falloff
            var coneRadius = coneAngle / 180.0f; // Convert to normalized radius
            var penumbraRadius = penumbraAngle / 180.0f;

            float coneEffect;
            if (normalizedDistance <= coneRadius)
            {
                // Inside main cone - full intensity
                coneEffect = 1.0f;
            }
            else if (normalizedDistance <= coneRadius + penumbraRadius)
            {
                // In penumbra - smooth falloff
                var penumbraFactor = (normalizedDistance - coneRadius) / penumbraRadius;
                coneEffect = 1.0f - penumbraFactor;
            }
            else
            {
                // Outside cone - no light
                coneEffect = 0.0f;
            }

            // Apply distance falloff
            var distanceFactor = 1.0f / (1.0f + depthRatio * 0.4f);

            return baseContribution * coneEffect * distanceFactor;
        }

        /// <summary>
        /// Calculate advanced area light contribution with size-based falloff
        /// </summary>
        private float CalculateAdvancedAreaLight(float baseContribution, float depthRatio, EnhancedLightingData lightingData)
        {
            // Calculate area light effect based on size
            var lightWidth = lightingData.AreaLightWidth;
            var lightHeight = lightingData.AreaLightHeight;
            var lightArea = lightWidth * lightHeight;

            // Larger area lights provide more even illumination
            var areaFactor = Math.Min(1.0f, lightArea / 2500.0f); // Normalize to reasonable range

            // Soft falloff based on area size
            var falloffRate = 0.15f * (1.0f - areaFactor * 0.5f); // Larger lights have gentler falloff
            var softFalloff = 1.0f - depthRatio * falloffRate;

            // Minimum illumination based on area size
            var minIllumination = 0.2f + areaFactor * 0.2f; // Larger lights maintain more ambient light

            return baseContribution * Math.Max(minIllumination, softFalloff);
        }

        /// <summary>
        /// Apply advanced light blending based on the selected mode
        /// </summary>
        private float ApplyAdvancedLightBlending(float lighting, ClockSettings settings)
        {
            // Use new BlendingMode enum if available, otherwise fall back to legacy mode
            var blendingMode = settings.BlendingMode;

            return blendingMode switch
            {
                LightBlendingMode.Additive => ApplyAdditiveBlending(lighting),
                LightBlendingMode.Multiplicative => ApplyMultiplicativeBlending(lighting),
                LightBlendingMode.Screen => ApplyScreenBlending(lighting),
                LightBlendingMode.Overlay => ApplyOverlayBlending(lighting),
                _ => ApplyLegacyBlending(lighting, settings.LegacyLightBlendingMode)
            };
        }

        /// <summary>
        /// Apply additive light blending - lights add together linearly
        /// </summary>
        private float ApplyAdditiveBlending(float lighting)
        {
            // Additive blending with saturation prevention
            return Math.Min(lighting, 2.0f); // Prevent over-saturation
        }

        /// <summary>
        /// Apply multiplicative light blending - lights interact naturally
        /// </summary>
        private float ApplyMultiplicativeBlending(float lighting)
        {
            // Multiplicative blending creates more realistic light interaction
            return lighting * 0.85f + (lighting * lighting * 0.15f);
        }

        /// <summary>
        /// Apply screen blending - bright areas become brighter
        /// </summary>
        private float ApplyScreenBlending(float lighting)
        {
            // Screen blending: 1 - (1 - a) * (1 - b)
            var inverted = 1.0f - lighting;
            return 1.0f - (inverted * inverted);
        }

        /// <summary>
        /// Apply overlay blending - combines multiply and screen
        /// </summary>
        private float ApplyOverlayBlending(float lighting)
        {
            // Overlay blending: multiply for dark areas, screen for bright areas
            if (lighting < 0.5f)
            {
                return 2.0f * lighting * lighting; // Multiply for dark areas
            }
            else
            {
                return 1.0f - 2.0f * (1.0f - lighting) * (1.0f - lighting); // Screen for bright areas
            }
        }

        /// <summary>
        /// Apply legacy light blending for backward compatibility
        /// </summary>
        private float ApplyLegacyBlending(float lighting, double blendingMode)
        {
            if (blendingMode < 1.0)
            {
                // Legacy additive blending
                var additiveStrength = (float)(1.0 - blendingMode);
                return lighting * (1.0f - additiveStrength * 0.3f);
            }
            else
            {
                // Legacy multiplicative blending
                var multiplicativeStrength = (float)(blendingMode - 1.0);
                return lighting * (1.0f + multiplicativeStrength * 0.2f);
            }
        }

        /// <summary>
        /// Calculate global illumination contribution
        /// </summary>
        private float CalculateGlobalIllumination(float depthRatio, ClockSettings settings)
        {
            if (!settings.EnableGlobalIllumination) return 0f;

            var bounces = settings.MaxLightBounces;
            var contribution = 0f;

            for (int i = 1; i <= bounces; i++)
            {
                var bounceIntensity = 0.1f / i; // Diminishing returns
                var bounceEffect = bounceIntensity * (1.0f - depthRatio * 0.5f);
                contribution += bounceEffect;
            }

            return contribution;
        }

        private SKBitmap RenderBasic3DText(string text, ClockSettings settings, SKPaint basePaint)
        {
            // Fallback to basic 3D rendering when Text3D module is not available
            var bitmap = new SKBitmap(800, 600);
            using var canvas = new SKCanvas(bitmap);

            // This is a simplified version - in a real implementation,
            // you would implement the basic 3D rendering logic here
            canvas.DrawText(text, 100, 100, basePaint);

            return bitmap;
        }

        private SKBitmap ApplyOpacityToBitmap(SKBitmap source, double opacity)
        {
            var result = new SKBitmap(source.Width, source.Height);
            using var canvas = new SKCanvas(result);
            using var paint = new SKPaint
            {
                Color = SKColors.White.WithAlpha((byte)(255 * opacity))
            };

            canvas.DrawBitmap(source, 0, 0, paint);
            return result;
        }

        private ClockSettings CreatePreviewSettings(ClockSettings settings)
        {
            // Create a copy with lower quality settings for preview
            var previewSettings = new ClockSettings();
            // Copy all properties but reduce quality
            // This is a simplified version - you would copy all relevant properties
            return previewSettings;
        }

        private string GenerateCacheKey(string text, ClockSettings settings, double opacity)
        {
            return $"{text}_{settings.FontFamily}_{settings.FontSize}_{settings.Material}_{opacity}";
        }

        private SKColor ToSKColor(System.Windows.Media.Brush brush)
        {
            if (brush is System.Windows.Media.SolidColorBrush solidBrush)
            {
                var color = solidBrush.Color;
                return new SKColor(color.R, color.G, color.B, color.A);
            }
            return SKColors.White;
        }

        /// <summary>
        /// Disposes of resources used by the Enhanced3DTextRenderer
        /// Last Updated: 2025-01-11 18:30:00 UTC
        /// </summary>
        [TestableMethod("ResourceManagement", IncludeExceptionTests = true, ExpectedExecutionTimeMs = 100)]
        public void Dispose()
        {
            lock (_cacheLock)
            {
                foreach (var bitmap in _renderCache.Values)
                {
                    bitmap?.Dispose();
                }
                _renderCache.Clear();
            }
        }
    }
}
