using System.Windows;
using ArtDesignFramework.AILighting;
using ArtDesignFramework.ClockDesktopApp.Services;
using ArtDesignFramework.ClockDesktopApp.ViewModels;
using ArtDesignFramework.Core;
using ArtDesignFramework.FreeFonts;
using ArtDesignFramework.FreeFonts.Core;
using ArtDesignFramework.Performance;
using ArtDesignFramework.UserInterface;
using ArtDesignFramework.UserInterface.Services.Rendering;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace ArtDesignFramework.ClockDesktopApp;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : System.Windows.Application
{
    private IHost? _host;

    protected override void OnStartup(StartupEventArgs e)
    {
        base.OnStartup(e);

        // Build the host with dependency injection
        _host = Host.CreateDefaultBuilder()
            .ConfigureServices((context, services) =>
            {
                // Add ArtDesignFramework services
                services.AddArtDesignFrameworkCore();
                services.AddPerformance();
                services.AddUserInterface();
                services.AddAILighting();

                // Add FreeFonts with FontRendering integration for enhanced font management
                services.AddFreeFontsWithFontRendering(options =>
                {
                    options.EnableLazyLoading = true;
                    options.PreloadCategories = new[] { FontCategory.SansSerif, FontCategory.Monospace };
                    options.EnablePerformanceMonitoring = true;
                    options.ValidateLicenses = true;
                });

                // Add application services
                services.AddSingleton<ClockWorkshopViewModel>();
                services.AddSingleton<ClockWidgetService>();

                // Register storage services
                services.AddSingleton<FileSettingsService>();

                // Register the main settings service with file-based storage for now
                services.AddSingleton<ISettingsStorageService, FileSettingsService>();
                services.AddSingleton<SettingsService>();

                services.AddSingleton<DateComponentManager>();
                services.AddSingleton<DragDropPreviewService>();
                services.AddSingleton<ClockLayerManager>();
                services.AddSingleton<IEnhanced3DTextRenderer, Enhanced3DTextRenderer>();
                services.AddSingleton<EnhancedFontLoadingService>();

                // Register SKPaint pool with 500-object configuration for enhanced performance
                services.AddSingleton<ISKPaintPool>(provider =>
                    new SKPaintPool(500, provider.GetService<ILogger<SKPaintPool>>()));

                // Add logging
                services.AddLogging(builder =>
                {
                    builder.AddConsole();
                    builder.AddDebug();
                    builder.SetMinimumLevel(LogLevel.Information);
                });
            })
            .Build();

        // Start the host
        _host.Start();

        // Set up the main window with dependency injection
        var mainWindow = new Views.ClockWorkshopWindow();
        var viewModel = _host.Services.GetRequiredService<ClockWorkshopViewModel>();
        mainWindow.DataContext = viewModel;

        MainWindow = mainWindow;
        mainWindow.Show();
    }

    protected override void OnExit(ExitEventArgs e)
    {
        _host?.Dispose();
        base.OnExit(e);
    }
}
