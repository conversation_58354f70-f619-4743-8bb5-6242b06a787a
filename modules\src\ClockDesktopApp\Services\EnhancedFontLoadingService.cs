using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using ArtDesignFramework.Core;
using ArtDesignFramework.FreeFonts.Core;
using ArtDesignFramework.Performance;
using Microsoft.Extensions.Logging;
using SkiaSharp;

namespace ArtDesignFramework.ClockDesktopApp.Services
{
    /// <summary>
    /// Enhanced font loading service with FreeFonts integration and comprehensive error handling
    /// Last Updated: 2025-01-11 18:45:00 UTC
    /// </summary>
    public class EnhancedFontLoadingService : IDisposable
    {
        private readonly ILogger<EnhancedFontLoadingService> _logger;
        private readonly IFreeFontLibrary _freeFontLibrary;
        private readonly IPerformanceMonitor _performanceMonitor;
        private readonly ConcurrentDictionary<string, SKTypeface> _loadedFonts;
        private readonly ConcurrentDictionary<string, string> _fontFilePaths;
        private readonly ConcurrentDictionary<string, DateTime> _fontLoadTimes;
        private readonly SemaphoreSlim _loadingSemaphore;
        private readonly Timer _cacheCleanupTimer;
        private bool _disposed;

        /// <summary>
        /// Initializes a new instance of the EnhancedFontLoadingService
        /// </summary>
        /// <param name="logger">Logger instance</param>
        /// <param name="freeFontLibrary">FreeFonts library service</param>
        /// <param name="performanceMonitor">Performance monitoring service</param>
        public EnhancedFontLoadingService(
            ILogger<EnhancedFontLoadingService> logger,
            IFreeFontLibrary freeFontLibrary,
            IPerformanceMonitor performanceMonitor)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _freeFontLibrary = freeFontLibrary ?? throw new ArgumentNullException(nameof(freeFontLibrary));
            _performanceMonitor = performanceMonitor ?? throw new ArgumentNullException(nameof(performanceMonitor));

            _loadedFonts = new ConcurrentDictionary<string, SKTypeface>();
            _fontFilePaths = new ConcurrentDictionary<string, string>();
            _fontLoadTimes = new ConcurrentDictionary<string, DateTime>();
            _loadingSemaphore = new SemaphoreSlim(5, 5); // Allow up to 5 concurrent font loads

            // Setup cache cleanup timer (runs every 30 minutes)
            _cacheCleanupTimer = new Timer(CleanupExpiredFonts, null,
                TimeSpan.FromMinutes(30), TimeSpan.FromMinutes(30));
        }

        /// <summary>
        /// Gets a typeface with comprehensive fallback mechanism
        /// Last Updated: 2025-01-11 18:45:00 UTC
        /// </summary>
        /// <param name="fontFamily">Font family name</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>SKTypeface instance</returns>
        [TestableMethod("FontRetrieval", IncludePerformanceTests = true, IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 200)]
        public async Task<SKTypeface> GetTypefaceAsync(string fontFamily, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(fontFamily))
            {
                _logger.LogWarning("Font family name is null or empty, using default font");
                return SKTypeface.Default;
            }

            using var activity = _performanceMonitor.StartActivity("FontLoading.GetTypeface");
            activity?.SetTag("FontFamily", fontFamily);

            try
            {
                // 1. Check if already loaded in cache
                if (_loadedFonts.TryGetValue(fontFamily, out var cachedTypeface))
                {
                    _fontLoadTimes[fontFamily] = DateTime.UtcNow; // Update access time
                    activity?.SetTag("Source", "Cache");
                    return cachedTypeface;
                }

                await _loadingSemaphore.WaitAsync(cancellationToken);
                try
                {
                    // Double-check after acquiring semaphore
                    if (_loadedFonts.TryGetValue(fontFamily, out cachedTypeface))
                    {
                        _fontLoadTimes[fontFamily] = DateTime.UtcNow;
                        activity?.SetTag("Source", "Cache");
                        return cachedTypeface;
                    }

                    // 2. Try to load from FreeFonts library
                    var freeFontTypeface = await TryLoadFromFreeFontsAsync(fontFamily, cancellationToken);
                    if (freeFontTypeface != null)
                    {
                        _loadedFonts[fontFamily] = freeFontTypeface;
                        _fontLoadTimes[fontFamily] = DateTime.UtcNow;
                        activity?.SetTag("Source", "FreeFonts");
                        _logger.LogInformation("Successfully loaded font '{FontFamily}' from FreeFonts library", fontFamily);
                        return freeFontTypeface;
                    }

                    // 3. Try to load from custom font file if path is known
                    if (_fontFilePaths.TryGetValue(fontFamily, out var fontPath))
                    {
                        var customTypeface = await TryLoadFromFileAsync(fontPath, cancellationToken);
                        if (customTypeface != null)
                        {
                            _loadedFonts[fontFamily] = customTypeface;
                            _fontLoadTimes[fontFamily] = DateTime.UtcNow;
                            activity?.SetTag("Source", "CustomFile");
                            _logger.LogInformation("Successfully loaded font '{FontFamily}' from custom file", fontFamily);
                            return customTypeface;
                        }
                    }

                    // 4. Try system font
                    var systemTypeface = SKTypeface.FromFamilyName(fontFamily);
                    if (systemTypeface != null && !systemTypeface.FamilyName.Equals("Arial", StringComparison.OrdinalIgnoreCase))
                    {
                        _loadedFonts[fontFamily] = systemTypeface;
                        _fontLoadTimes[fontFamily] = DateTime.UtcNow;
                        activity?.SetTag("Source", "System");
                        _logger.LogInformation("Successfully loaded font '{FontFamily}' from system", fontFamily);
                        return systemTypeface;
                    }

                    // 5. Final fallback to default
                    _logger.LogWarning("Font '{FontFamily}' not found, using default font", fontFamily);
                    activity?.SetTag("Source", "Default");
                    return SKTypeface.Default;
                }
                finally
                {
                    _loadingSemaphore.Release();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading font '{FontFamily}', using default font", fontFamily);
                activity?.SetTag("Error", ex.Message);
                return SKTypeface.Default;
            }
        }

        /// <summary>
        /// Loads a custom font file with comprehensive validation and error handling
        /// Last Updated: 2025-01-11 18:45:00 UTC
        /// </summary>
        /// <param name="fontFilePath">Path to the font file</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if font was loaded successfully</returns>
        [TestableMethod("CustomFontLoading", IncludePerformanceTests = true, IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 500)]
        public async Task<bool> LoadCustomFontAsync(string fontFilePath, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(fontFilePath))
            {
                _logger.LogWarning("Font file path is null or empty");
                return false;
            }

            using var activity = _performanceMonitor.StartActivity("FontLoading.LoadCustomFont");
            activity?.SetTag("FontFilePath", fontFilePath);

            try
            {
                // Validate file exists and is readable
                if (!await ValidateFontFileAsync(fontFilePath, cancellationToken))
                {
                    return false;
                }

                var typeface = await TryLoadFromFileAsync(fontFilePath, cancellationToken);
                if (typeface == null)
                {
                    return false;
                }

                // Test font rendering capability
                if (!await TestFontRenderingAsync(typeface, cancellationToken))
                {
                    typeface.Dispose();
                    return false;
                }

                var fontName = GetValidFontName(typeface, fontFilePath);
                if (string.IsNullOrWhiteSpace(fontName))
                {
                    typeface.Dispose();
                    return false;
                }

                // Store the font
                _loadedFonts[fontName] = typeface;
                _fontFilePaths[fontName] = fontFilePath;
                _fontLoadTimes[fontName] = DateTime.UtcNow;

                _logger.LogInformation("Successfully loaded custom font '{FontName}' from '{FontFilePath}'", fontName, fontFilePath);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to load custom font from '{FontFilePath}'", fontFilePath);
                activity?.SetTag("Error", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Gets all available fonts including FreeFonts and custom fonts
        /// Last Updated: 2025-01-11 18:45:00 UTC
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of available font names</returns>
        [TestableMethod("FontEnumeration", IncludePerformanceTests = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 1000)]
        public async Task<IEnumerable<string>> GetAvailableFontsAsync(CancellationToken cancellationToken = default)
        {
            using var activity = _performanceMonitor.StartActivity("FontLoading.GetAvailableFonts");

            var availableFonts = new HashSet<string>();

            try
            {
                // Add FreeFonts library fonts
                if (_freeFontLibrary.IsInitialized)
                {
                    var freeFonts = await _freeFontLibrary.GetAvailableFontsAsync(cancellationToken);
                    foreach (var font in freeFonts)
                    {
                        availableFonts.Add(font.FontFamily);
                    }
                }

                // Add custom loaded fonts
                foreach (var fontName in _loadedFonts.Keys)
                {
                    availableFonts.Add(fontName);
                }

                // Add common system fonts
                var systemFonts = new[] { "Arial", "Times New Roman", "Courier New", "Verdana", "Tahoma", "Georgia" };
                foreach (var systemFont in systemFonts)
                {
                    availableFonts.Add(systemFont);
                }

                activity?.SetTag("FontCount", availableFonts.Count);
                return availableFonts.OrderBy(f => f).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting available fonts");
                activity?.SetTag("Error", ex.Message);
                return new[] { "Arial", "Times New Roman", "Courier New" }; // Minimal fallback
            }
        }

        /// <summary>
        /// Clears the font cache and releases resources
        /// Last Updated: 2025-01-11 18:45:00 UTC
        /// </summary>
        [TestableMethod("CacheManagement", IncludeExceptionTests = true, ExpectedExecutionTimeMs = 100)]
        public void ClearCache()
        {
            using var activity = _performanceMonitor.StartActivity("FontLoading.ClearCache");

            try
            {
                foreach (var typeface in _loadedFonts.Values)
                {
                    typeface?.Dispose();
                }

                _loadedFonts.Clear();
                _fontFilePaths.Clear();
                _fontLoadTimes.Clear();

                _logger.LogInformation("Font cache cleared successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing font cache");
                activity?.SetTag("Error", ex.Message);
            }
        }

        /// <summary>
        /// Tries to load font from FreeFonts library
        /// </summary>
        private async Task<SKTypeface?> TryLoadFromFreeFontsAsync(string fontFamily, CancellationToken cancellationToken)
        {
            try
            {
                if (!_freeFontLibrary.IsInitialized)
                {
                    await _freeFontLibrary.InitializeAsync(cancellationToken);
                }

                var fontInfo = await _freeFontLibrary.LoadFontAsync(fontFamily, cancellationToken: cancellationToken);
                return fontInfo?.Typeface;
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "Failed to load font '{FontFamily}' from FreeFonts library", fontFamily);
                return null;
            }
        }

        /// <summary>
        /// Tries to load font from file with retry logic
        /// </summary>
        private async Task<SKTypeface?> TryLoadFromFileAsync(string fontFilePath, CancellationToken cancellationToken)
        {
            const int maxRetries = 3;
            const int retryDelayMs = 100;

            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    var fontData = await File.ReadAllBytesAsync(fontFilePath, cancellationToken);
                    var skData = SKData.CreateCopy(fontData);
                    var typeface = SKTypeface.FromData(skData);

                    if (typeface != null)
                    {
                        return typeface;
                    }
                }
                catch (Exception ex) when (attempt < maxRetries)
                {
                    _logger.LogDebug(ex, "Font loading attempt {Attempt} failed for '{FontFilePath}', retrying...", attempt, fontFilePath);
                    await Task.Delay(retryDelayMs * attempt, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to load font from file '{FontFilePath}' after {MaxRetries} attempts", fontFilePath, maxRetries);
                }
            }

            return null;
        }

        /// <summary>
        /// Validates font file integrity and accessibility
        /// </summary>
        private async Task<bool> ValidateFontFileAsync(string fontFilePath, CancellationToken cancellationToken)
        {
            try
            {
                if (!File.Exists(fontFilePath))
                {
                    _logger.LogWarning("Font file does not exist: '{FontFilePath}'", fontFilePath);
                    return false;
                }

                var fileInfo = new FileInfo(fontFilePath);
                if (fileInfo.Length == 0)
                {
                    _logger.LogWarning("Font file is empty: '{FontFilePath}'", fontFilePath);
                    return false;
                }

                if (fileInfo.Length > 50 * 1024 * 1024) // 50MB limit
                {
                    _logger.LogWarning("Font file is too large ({Size} bytes): '{FontFilePath}'", fileInfo.Length, fontFilePath);
                    return false;
                }

                // Check file extension
                var extension = Path.GetExtension(fontFilePath).ToLowerInvariant();
                var validExtensions = new[] { ".ttf", ".otf", ".woff", ".woff2" };
                if (!validExtensions.Contains(extension))
                {
                    _logger.LogWarning("Unsupported font file extension '{Extension}': '{FontFilePath}'", extension, fontFilePath);
                    return false;
                }

                // Try to read first few bytes to validate file header
                using var stream = new FileStream(fontFilePath, FileMode.Open, FileAccess.Read, FileShare.Read);
                var buffer = new byte[4];
                await stream.ReadAsync(buffer, 0, 4, cancellationToken);

                // Check for common font file signatures
                var signature = BitConverter.ToUInt32(buffer, 0);
                var validSignatures = new uint[] { 0x00010000, 0x4F54544F, 0x774F4646, 0x774F4632 }; // TTF, OTF, WOFF, WOFF2

                return validSignatures.Any(sig => signature == sig || BitConverter.ToUInt32(buffer.Reverse().ToArray(), 0) == sig);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error validating font file '{FontFilePath}'", fontFilePath);
                return false;
            }
        }

        /// <summary>
        /// Tests font rendering capability
        /// </summary>
        private async Task<bool> TestFontRenderingAsync(SKTypeface typeface, CancellationToken cancellationToken)
        {
            return await Task.Run(() =>
            {
                try
                {
                    using var paint = new SKPaint
                    {
                        Typeface = typeface,
                        TextSize = 24,
                        IsAntialias = true
                    };

                    // Test with common clock characters
                    var testText = "0123456789:. AM PM";
                    var bounds = new SKRect();
                    var width = paint.MeasureText(testText, ref bounds);

                    // Font is valid if it can measure text and has reasonable bounds
                    return width > 0 && bounds.Width > 0 && bounds.Height > 0;
                }
                catch (Exception ex)
                {
                    _logger.LogDebug(ex, "Font rendering test failed for typeface: {FamilyName}", typeface.FamilyName);
                    return false;
                }
            }, cancellationToken);
        }

        /// <summary>
        /// Gets a valid font name from typeface
        /// </summary>
        private string GetValidFontName(SKTypeface typeface, string fontFilePath)
        {
            try
            {
                var fontName = typeface.FamilyName;
                if (!string.IsNullOrWhiteSpace(fontName))
                {
                    return fontName;
                }

                // Fallback to filename without extension
                return Path.GetFileNameWithoutExtension(fontFilePath);
            }
            catch
            {
                return Path.GetFileNameWithoutExtension(fontFilePath);
            }
        }

        /// <summary>
        /// Cleanup expired fonts from cache
        /// </summary>
        private void CleanupExpiredFonts(object? state)
        {
            try
            {
                var expiredFonts = _fontLoadTimes
                    .Where(kvp => DateTime.UtcNow - kvp.Value > TimeSpan.FromHours(2))
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var fontName in expiredFonts)
                {
                    if (_loadedFonts.TryRemove(fontName, out var typeface))
                    {
                        typeface?.Dispose();
                        _fontLoadTimes.TryRemove(fontName, out _);
                        _fontFilePaths.TryRemove(fontName, out _);
                    }
                }

                if (expiredFonts.Count > 0)
                {
                    _logger.LogDebug("Cleaned up {Count} expired fonts from cache", expiredFonts.Count);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error during font cache cleanup");
            }
        }

        /// <summary>
        /// Disposes of resources used by the EnhancedFontLoadingService
        /// Last Updated: 2025-01-11 18:45:00 UTC
        /// </summary>
        [TestableMethod("ResourceManagement", IncludeExceptionTests = true, ExpectedExecutionTimeMs = 200)]
        public void Dispose()
        {
            if (_disposed) return;

            try
            {
                _cacheCleanupTimer?.Dispose();
                _loadingSemaphore?.Dispose();

                foreach (var typeface in _loadedFonts.Values)
                {
                    typeface?.Dispose();
                }

                _loadedFonts.Clear();
                _fontFilePaths.Clear();
                _fontLoadTimes.Clear();

                _disposed = true;
                _logger.LogInformation("EnhancedFontLoadingService disposed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disposing EnhancedFontLoadingService");
            }
        }
    }
}
