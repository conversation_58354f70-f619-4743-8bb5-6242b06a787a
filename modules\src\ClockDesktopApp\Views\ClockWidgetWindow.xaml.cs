using System.IO;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Threading;
using ArtDesignFramework.ClockDesktopApp.Services;
using ArtDesignFramework.UserInterface.Services.Rendering;
using SkiaSharp;
using SkiaSharp.Views.Desktop;
using SkiaSharp.Views.WPF;
using WpfBrush = System.Windows.Media.Brush;
using WpfPoint = System.Windows.Point;

namespace ArtDesignFramework.ClockDesktopApp.Views;

/// <summary>
/// Desktop clock widget window
/// </summary>
public partial class ClockWidgetWindow : Window
{
    private ClockSettings _settings;
    private DispatcherTimer _updateTimer;
    private WpfPoint _dragStartPoint;
    private bool _isDragging;
    private WpfPoint _lastMousePosition;
    private readonly Dictionary<string, SKTypeface> _loadedFonts = new();
    private readonly ISKPaintPool _paintPool;
    private readonly EnhancedFontLoadingService _enhancedFontService;

    public ClockWidgetWindow(ClockSettings settings, ISKPaintPool paintPool, EnhancedFontLoadingService enhancedFontService)
    {
        InitializeComponent();
        _settings = settings;
        _paintPool = paintPool ?? throw new ArgumentNullException(nameof(paintPool));
        _enhancedFontService = enhancedFontService ?? throw new ArgumentNullException(nameof(enhancedFontService));
        CurrentSettings = _settings; // Initialize static property for XAML binding

        // Load custom fonts
        LoadCustomFonts();

        // Set up the update timer
        _updateTimer = new DispatcherTimer
        {
            Interval = TimeSpan.FromSeconds(1)
        };
        _updateTimer.Tick += UpdateTimer_Tick;
        _updateTimer.Start();

        // Apply initial settings
        ApplySettings();
    }

    private void UpdateTimer_Tick(object? sender, EventArgs e)
    {
        // Invalidate the clock display to trigger a repaint
        ClockDisplay.InvalidateVisual();
    }

    private void ClockDisplay_PaintSurface(object? sender, SKPaintSurfaceEventArgs e)
    {
        var canvas = e.Surface.Canvas;
        var info = e.Info;

        // Clear the canvas
        canvas.Clear(SKColors.Transparent);

        // Render the clock
        RenderClock(canvas, info.Width, info.Height);
    }

    private void RenderClock(SKCanvas canvas, int width, int height)
    {
        try
        {
            // Clear canvas
            canvas.Clear(SKColors.Transparent);

            // Render background shape if not transparent and no background image
            if (!_settings.TransparentBackground && string.IsNullOrEmpty(_settings.BackgroundImagePath))
            {
                RenderBackgroundShape(canvas, width, height);
            }

            // Render background image if set (replaces background color)
            if (!string.IsNullOrEmpty(_settings.BackgroundImagePath) && !_settings.TransparentBackground)
            {
                RenderBackgroundImage(canvas, width, height);
            }

            // Get current time
            var currentTime = DateTime.Now;
            string timeString;

            if (_settings.Use24Hour)
            {
                timeString = currentTime.ToString("HH:mm");
            }
            else
            {
                timeString = _settings.ShowAmPm ? currentTime.ToString("hh:mm tt") : currentTime.ToString("hh:mm");
            }

            // Get paint from pool for text rendering
            var paint = _paintPool.Get();
            try
            {
                paint.Color = GetEnhancedTextColor(_settings.TextColor);
                paint.TextSize = (float)_settings.FontSize;
                paint.IsAntialias = true;
                paint.Typeface = GetTypeface(_settings.FontFamily);

                // Measure and center text
                var textBounds = new SKRect();
                paint.MeasureText(timeString, ref textBounds);

                var x = (width - textBounds.Width) / 2;
                var y = (height - textBounds.Height) / 2 + textBounds.Height;

                // Draw glow effect if enabled (proper glow, not just blur)
                if (_settings.EnableGlow)
                {
                    DrawGlowEffect(canvas, timeString, x, y, paint);
                }

                // Draw shadow if enabled (enhanced for transparent mode)
                if (_settings.EnableShadow || _settings.TransparentBackground)
                {
                    using var shadowPaint = paint.Clone();

                    // Use enhanced shadow for transparent mode to ensure text visibility
                    if (_settings.TransparentBackground && !_settings.EnableShadow)
                    {
                        // Auto-shadow for transparent mode
                        shadowPaint.Color = SKColors.Black.WithAlpha(180);
                        shadowPaint.MaskFilter = SKMaskFilter.CreateBlur(SKBlurStyle.Normal, 2.0f);
                        canvas.DrawText(timeString, x + 1, y + 1, shadowPaint);
                    }
                    else if (_settings.EnableShadow)
                    {
                        // User-configured shadow
                        shadowPaint.Color = GetEnhancedShadowColor(_settings.ShadowColor);
                        if (_settings.ShadowBlur > 0)
                        {
                            shadowPaint.MaskFilter = SKMaskFilter.CreateBlur(SKBlurStyle.Normal, (float)_settings.ShadowBlur);
                        }
                        canvas.DrawText(timeString, x + (float)_settings.ShadowOffsetX, y + (float)_settings.ShadowOffsetY, shadowPaint);
                    }
                }

                // Draw stroke if enabled (enhanced for transparent mode)
                if (_settings.EnableStroke || (_settings.TransparentBackground && !_settings.EnableShadow))
                {
                    using var strokePaint = paint.Clone();
                    strokePaint.Style = SKPaintStyle.Stroke;

                    if (_settings.TransparentBackground && !_settings.EnableStroke)
                    {
                        // Auto-stroke for transparent mode when no shadow is enabled
                        strokePaint.StrokeWidth = 1.5f;
                        strokePaint.Color = SKColors.Black.WithAlpha(200);
                    }
                    else
                    {
                        // User-configured stroke
                        strokePaint.StrokeWidth = (float)_settings.StrokeWidth;
                        strokePaint.Color = GetEnhancedStrokeColor(_settings.StrokeColor);
                    }

                    canvas.DrawText(timeString, x, y, strokePaint);
                }

                // Draw main text (3D or regular)
                if (_settings.Enable3D)
                {
                    Draw3DText(canvas, timeString, x, y, paint);
                }
                else
                {
                    canvas.DrawText(timeString, x, y, paint);
                }

                // Draw date if enabled (with enhanced visibility for transparent mode)
                if (_settings.ShowDate)
                {
                    var dateString = currentTime.ToString("dddd, MMMM d, yyyy");
                    using var datePaint = paint.Clone();
                    datePaint.Color = GetEnhancedTextColor(_settings.DateColor);
                    datePaint.TextSize = paint.TextSize * 0.4f;

                    var dateBounds = new SKRect();
                    datePaint.MeasureText(dateString, ref dateBounds);
                    var dateX = (width - dateBounds.Width) / 2;
                    var dateY = y + dateBounds.Height + 10;

                    // Add shadow/stroke for date in transparent mode
                    if (_settings.TransparentBackground)
                    {
                        // Draw date shadow for better visibility
                        using var dateShadowPaint = datePaint.Clone();
                        dateShadowPaint.Color = SKColors.Black.WithAlpha(150);
                        dateShadowPaint.MaskFilter = SKMaskFilter.CreateBlur(SKBlurStyle.Normal, 1.5f);
                        canvas.DrawText(dateString, dateX + 1, dateY + 1, dateShadowPaint);
                    }

                    canvas.DrawText(dateString, dateX, dateY, datePaint);
                }
            }
            }
        finally
        {
            _paintPool.Return(paint);
        }

        /// <summary>
        /// Updates the widget with new clock settings
        /// Last Updated: 2025-01-11 18:30:00 UTC
        /// </summary>
        /// <param name="settings">New settings to apply</param>
        [TestableMethod("WidgetUpdate", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 100)]
        public void UpdateSettings(ClockSettings settings)
        {
            _settings = settings;
            ApplySettings();
            ClockDisplay.InvalidateVisual();
        }

    private void ApplySettings()
    {
        // Apply window properties
        Topmost = _settings.AlwaysOnTop;
        Opacity = _settings.Opacity;

        // Configure transparent borderless mode
        if (_settings.TransparentBackground)
        {
            // Truly transparent background with no window chrome
            Background = System.Windows.Media.Brushes.Transparent;
            MainBorder.Background = System.Windows.Media.Brushes.Transparent;
            MainBorder.BorderBrush = System.Windows.Media.Brushes.Transparent;
            MainBorder.BorderThickness = new Thickness(0);

            // Hide resize grip in transparent mode for clean borderless appearance
            ResizeGrip.Visibility = Visibility.Collapsed;

            // Ensure window chrome is completely removed
            WindowStyle = WindowStyle.None;
            AllowsTransparency = true;
            ShowInTaskbar = false;

            // Adjust margins for borderless mode
            ClockDisplay.Margin = new Thickness(0);
        }
        else
        {
            // Non-transparent mode with visible window elements
            Background = _settings.BackgroundColor;
            MainBorder.Background = System.Windows.Media.Brushes.Transparent;
            MainBorder.BorderBrush = System.Windows.Media.Brushes.Transparent;
            MainBorder.BorderThickness = new Thickness(0);

            // Show resize grip in non-transparent mode if resizable
            ResizeGrip.Visibility = _settings.Resizable ? Visibility.Visible : Visibility.Collapsed;

            // Restore normal margins
            ClockDisplay.Margin = new Thickness(10);
        }

        // Apply window positioning and sizing
        ApplyWindowBounds();

        // Update context menu state
        UpdateContextMenuState();
    }

    /// <summary>
    /// Updates the context menu items to reflect current settings
    /// </summary>
    private void UpdateContextMenuState()
    {
        try
        {
            if (TransparentBackgroundMenuItem != null)
            {
                TransparentBackgroundMenuItem.IsChecked = _settings.TransparentBackground;
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error updating context menu state: {ex.Message}");
        }
    }

    /// <summary>
    /// Applies window bounds and positioning for transparent borderless mode
    /// </summary>
    private void ApplyWindowBounds()
    {
        try
        {
            // Apply saved position and size
            if (_settings.Left >= 0 && _settings.Top >= 0)
            {
                Left = _settings.Left;
                Top = _settings.Top;
            }

            if (_settings.Width > 0 && _settings.Height > 0)
            {
                Width = _settings.Width;
                Height = _settings.Height;
            }

            // Ensure window stays within screen bounds for transparent mode
            if (_settings.TransparentBackground)
            {
                EnsureWindowInScreenBounds();
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error applying window bounds: {ex.Message}");
        }
    }

    /// <summary>
    /// Ensures the window remains within screen bounds for transparent borderless mode
    /// </summary>
    private void EnsureWindowInScreenBounds()
    {
        try
        {
            var screenWidth = SystemParameters.PrimaryScreenWidth;
            var screenHeight = SystemParameters.PrimaryScreenHeight;

            // Ensure window is not completely off-screen
            if (Left + Width < 50) Left = 0;
            if (Top + Height < 50) Top = 0;
            if (Left > screenWidth - 50) Left = screenWidth - Width;
            if (Top > screenHeight - 50) Top = screenHeight - Height;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error ensuring window bounds: {ex.Message}");
        }
    }

    private void Window_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
    {
        if (_settings.Draggable)
        {
            _isDragging = true;
            _dragStartPoint = e.GetPosition(this);
            _lastMousePosition = PointToScreen(e.GetPosition(this));
            CaptureMouse();

            // In transparent mode, ensure we can still drag the window
            if (_settings.TransparentBackground)
            {
                try
                {
                    DragMove();
                }
                catch (InvalidOperationException)
                {
                    // Handle case where DragMove is called when mouse is not pressed
                    // Fall back to manual dragging
                }
            }
        }
    }

    private void Window_MouseMove(object sender, System.Windows.Input.MouseEventArgs e)
    {
        if (_isDragging && _settings.Draggable)
        {
            var currentMousePosition = PointToScreen(e.GetPosition(this));
            var deltaX = currentMousePosition.X - _lastMousePosition.X;
            var deltaY = currentMousePosition.Y - _lastMousePosition.Y;

            var newLeft = Left + deltaX;
            var newTop = Top + deltaY;

            // Allow positioning at screen edges (no constraints)
            Left = newLeft;
            Top = newTop;

            _lastMousePosition = currentMousePosition;
        }
    }

    private void Window_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
    {
        if (_isDragging)
        {
            _isDragging = false;
            ReleaseMouseCapture();

            // Save position for transparent borderless mode
            SaveWindowPosition();
        }
    }

    /// <summary>
    /// Saves the current window position to settings
    /// </summary>
    private void SaveWindowPosition()
    {
        try
        {
            _settings.Left = Left;
            _settings.Top = Top;
            _settings.Width = (int)Width;
            _settings.Height = (int)Height;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error saving window position: {ex.Message}");
        }
    }

    private void Window_MouseRightButtonDown(object sender, MouseButtonEventArgs e)
    {
        if (ContextMenu != null)
        {
            ContextMenu.IsOpen = true;
        }
    }

    private void Window_MouseDoubleClick(object sender, MouseButtonEventArgs e)
    {
        try
        {
            // Double-click opens full settings (same as context menu option)
            Settings_Click(sender, e);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error handling double-click: {ex.Message}");
        }
    }

    private void ResizeGrip_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
    {
        if (_settings.Resizable && !_settings.TransparentBackground)
        {
            // Resize grip is only functional in non-transparent mode
            // In transparent mode, the resize grip is hidden for borderless appearance
            e.Handled = true;
        }
    }

    private void Settings_Click(object sender, RoutedEventArgs e)
    {
        // TODO: Open settings window or return to workshop
        System.Windows.MessageBox.Show("Settings functionality will open the Clock Workshop window.", "Settings", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void AlwaysOnTop_Click(object sender, RoutedEventArgs e)
    {
        _settings.AlwaysOnTop = !_settings.AlwaysOnTop;
        Topmost = _settings.AlwaysOnTop;
    }

    private void Close_Click(object sender, RoutedEventArgs e)
    {
        Close();
    }

    /// <summary>
    /// Static property to expose current settings for XAML binding
    /// </summary>
    public static ClockSettings? CurrentSettings { get; private set; }

    /// <summary>
    /// Context menu event handlers for quick customization access
    /// </summary>
    private void TransparentBackground_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            _settings.TransparentBackground = !_settings.TransparentBackground;
            ApplySettings();
            ClockDisplay.InvalidateVisual();

            // Show feedback
            ShowQuickFeedback($"Transparent background: {(_settings.TransparentBackground ? "ON" : "OFF")}");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error toggling transparent background: {ex.Message}");
        }
    }

    private void QuickColor_White_Click(object sender, RoutedEventArgs e)
    {
        ApplyQuickColor(System.Windows.Media.Brushes.White, "White");
    }

    private void QuickColor_Red_Click(object sender, RoutedEventArgs e)
    {
        ApplyQuickColor(System.Windows.Media.Brushes.Red, "Red");
    }

    private void QuickColor_Green_Click(object sender, RoutedEventArgs e)
    {
        ApplyQuickColor(System.Windows.Media.Brushes.LimeGreen, "Green");
    }

    private void QuickColor_Blue_Click(object sender, RoutedEventArgs e)
    {
        ApplyQuickColor(System.Windows.Media.Brushes.DodgerBlue, "Blue");
    }

    private void QuickColor_Yellow_Click(object sender, RoutedEventArgs e)
    {
        ApplyQuickColor(System.Windows.Media.Brushes.Yellow, "Yellow");
    }

    private void ApplyQuickColor(WpfBrush color, string colorName)
    {
        try
        {
            _settings.TextColor = color;
            _settings.DateColor = color;
            ClockDisplay.InvalidateVisual();

            // Show feedback
            ShowQuickFeedback($"Text color changed to {colorName}");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error applying quick color: {ex.Message}");
        }
    }

    private void ResetDefaults_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var result = System.Windows.MessageBox.Show(
                "Reset all settings to factory defaults?\n\nThis will restore the original clock appearance and cannot be undone.",
                "Reset to Factory Defaults",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question,
                MessageBoxResult.No);

            if (result == MessageBoxResult.Yes)
            {
                // Reset to factory defaults while preserving window position
                _settings.ResetToFactoryDefaults(preservePosition: true);
                ApplySettings();
                ClockDisplay.InvalidateVisual();

                ShowQuickFeedback("Settings reset to factory defaults");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error resetting to factory defaults: {ex.Message}");
            ShowQuickFeedback("Failed to reset settings");
        }
    }

    /// <summary>
    /// Shows quick feedback message to user
    /// </summary>
    private void ShowQuickFeedback(string message)
    {
        try
        {
            // Create a simple tooltip-style feedback
            var tooltip = new System.Windows.Controls.ToolTip
            {
                Content = message,
                IsOpen = true,
                Placement = System.Windows.Controls.Primitives.PlacementMode.Center,
                PlacementTarget = this
            };

            // Auto-hide after 2 seconds
            var timer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(2)
            };
            timer.Tick += (s, e) =>
            {
                tooltip.IsOpen = false;
                timer.Stop();
            };
            timer.Start();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error showing feedback: {ex.Message}");
        }
    }

    protected override void OnClosed(EventArgs e)
    {
        if (_updateTimer != null)
        {
            _updateTimer.Stop();
        }

        // Log final paint pool statistics for performance monitoring
        LogPaintPoolStatistics();

        base.OnClosed(e);
    }

    /// <summary>
    /// Logs paint pool performance statistics for monitoring 70% memory reduction target
    /// </summary>
    private void LogPaintPoolStatistics()
    {
        try
        {
            if (_paintPool is SKPaintPool pool)
            {
                var stats = pool.GetStatistics();
                var memoryReduction = stats.PoolEfficiency * 100;

                System.Diagnostics.Debug.WriteLine($"ClockWidget Paint Pool Statistics:");
                System.Diagnostics.Debug.WriteLine($"  Total Created: {stats.TotalCreated}");
                System.Diagnostics.Debug.WriteLine($"  Reuse Count: {stats.ReuseCount}");
                System.Diagnostics.Debug.WriteLine($"  Pool Efficiency: {stats.PoolEfficiency:P2}");
                System.Diagnostics.Debug.WriteLine($"  Memory Reduction: {memoryReduction:F1}% (Target: 70%)");
                System.Diagnostics.Debug.WriteLine($"  Available Objects: {stats.AvailableCount}");

                // Check if we're meeting the 70% memory reduction target
                if (memoryReduction >= 70.0)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ Memory reduction target achieved!");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ Memory reduction below target (70%)");
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error logging paint pool statistics: {ex.Message}");
        }
    }

    private void RenderBackgroundShape(SKCanvas canvas, int width, int height)
    {
        try
        {
            // Calculate text bounds for background sizing
            var textBounds = CalculateTextBounds();

            // Calculate background dimensions
            float backgroundWidth, backgroundHeight;
            if (_settings.BackgroundWidth > 0 && _settings.BackgroundHeight > 0)
            {
                // Use custom dimensions if specified
                backgroundWidth = (float)_settings.BackgroundWidth;
                backgroundHeight = (float)_settings.BackgroundHeight;
            }
            else
            {
                // Default: 25% larger than text bounds
                var textWidth = textBounds.Width;
                var textHeight = textBounds.Height;
                backgroundWidth = textWidth * 1.25f;
                backgroundHeight = textHeight * 1.25f;

                // Ensure minimum size
                backgroundWidth = Math.Max(backgroundWidth, 50);
                backgroundHeight = Math.Max(backgroundHeight, 30);
            }

            // Calculate background position with offsets
            var centerX = width / 2f + (float)_settings.BackgroundOffsetX;
            var centerY = height / 2f + (float)_settings.BackgroundOffsetY;

            // Create background rectangle centered with offsets
            var left = centerX - backgroundWidth / 2f;
            var top = centerY - backgroundHeight / 2f;
            var right = left + backgroundWidth;
            var bottom = top + backgroundHeight;

            var rect = new SKRect(left, top, right, bottom);

            var paint = _paintPool.Get();
            try
            {
                paint.Color = ToSKColor(_settings.BackgroundColor);
                paint.IsAntialias = true;
                paint.Style = SKPaintStyle.Fill;

                // Apply feather/blur effect if enabled
                if (_settings.BackgroundFeather > 0)
                {
                    paint.MaskFilter = SKMaskFilter.CreateBlur(SKBlurStyle.Normal, (float)_settings.BackgroundFeather);
                }

                // Draw shape based on background shape setting
                switch (_settings.BackgroundShape?.ToLower())
                {
                    case "circle":
                        var radius = Math.Min(backgroundWidth, backgroundHeight) / 2;
                        canvas.DrawCircle(centerX, centerY, radius, paint);
                        break;
                    case "ellipse":
                        canvas.DrawOval(rect, paint);
                        break;
                    case "roundedrectangle":
                        canvas.DrawRoundRect(rect, (float)_settings.BackgroundCornerRadius, (float)_settings.BackgroundCornerRadius, paint);
                        break;
                    default: // Rectangle
                        canvas.DrawRect(rect, paint);
                        break;
                }
            }
            finally
            {
                _paintPool.Return(paint);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error rendering background shape: {ex.Message}");
        }
    }

    /// <summary>
    /// Calculates the text bounds for background sizing
    /// </summary>
    private SKRect CalculateTextBounds()
    {
        try
        {
            // Get current time string
            var currentTime = DateTime.Now;
            string timeString;

            if (_settings.Use24Hour)
            {
                timeString = currentTime.ToString("HH:mm");
            }
            else
            {
                timeString = _settings.ShowAmPm ? currentTime.ToString("hh:mm tt") : currentTime.ToString("hh:mm");
            }

            // Create paint for text measurement
            using var paint = new SKPaint
            {
                TextSize = (float)_settings.FontSize,
                IsAntialias = true,
                Typeface = GetTypeface(_settings.FontFamily)
            };

            // Measure text bounds
            var textBounds = new SKRect();
            paint.MeasureText(timeString, ref textBounds);

            return textBounds;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error calculating text bounds: {ex.Message}");
            // Return default bounds if calculation fails
            return new SKRect(0, 0, 200, 50);
        }
    }

    /// <summary>
    /// Renders background image with seamless borderless integration (Widget mode)
    /// </summary>
    private void RenderBackgroundImage(SKCanvas canvas, int width, int height)
    {
        try
        {
            if (string.IsNullOrEmpty(_settings.BackgroundImagePath) || !File.Exists(_settings.BackgroundImagePath))
                return;

            using var imageData = SKData.Create(_settings.BackgroundImagePath);
            using var image = SKImage.FromEncodedData(imageData);

            if (image == null)
                return;

            using var paint = new SKPaint
            {
                IsAntialias = true,
                FilterQuality = SKFilterQuality.High
            };

            // Create clipping path to ensure seamless integration
            canvas.Save();

            // Determine rendering mode based on background shape
            if (_settings.BackgroundShape?.ToLower() == "circle")
            {
                RenderBackgroundImageCircular(canvas, image, paint, width, height);
            }
            else if (_settings.BackgroundShape?.ToLower() == "ellipse")
            {
                RenderBackgroundImageElliptical(canvas, image, paint, width, height);
            }
            else if (_settings.BackgroundShape?.ToLower() == "roundedrectangle")
            {
                RenderBackgroundImageRoundedRect(canvas, image, paint, width, height);
            }
            else
            {
                RenderBackgroundImageRectangular(canvas, image, paint, width, height);
            }

            canvas.Restore();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error rendering background image: {ex.Message}");
        }
    }

    /// <summary>
    /// Renders background image in rectangular mode with seamless borders (Widget mode)
    /// </summary>
    private void RenderBackgroundImageRectangular(SKCanvas canvas, SKImage image, SKPaint paint, int width, int height)
    {
        // Calculate text bounds for proper sizing
        var textBounds = CalculateTextBounds();

        // Calculate background dimensions (25% larger than text by default)
        float backgroundWidth, backgroundHeight;
        if (_settings.BackgroundWidth > 0 && _settings.BackgroundHeight > 0)
        {
            backgroundWidth = (float)_settings.BackgroundWidth;
            backgroundHeight = (float)_settings.BackgroundHeight;
        }
        else
        {
            backgroundWidth = textBounds.Width * 1.25f;
            backgroundHeight = textBounds.Height * 1.25f;
            backgroundWidth = Math.Max(backgroundWidth, 100);
            backgroundHeight = Math.Max(backgroundHeight, 60);
        }

        // Calculate position with offsets
        var centerX = width / 2f + (float)_settings.BackgroundOffsetX;
        var centerY = height / 2f + (float)_settings.BackgroundOffsetY;

        var left = centerX - backgroundWidth / 2f;
        var top = centerY - backgroundHeight / 2f;
        var right = left + backgroundWidth;
        var bottom = top + backgroundHeight;

        var destRect = new SKRect(left, top, right, bottom);

        // Apply corner radius clipping if specified
        if (_settings.BackgroundCornerRadius > 0)
        {
            var clipPath = new SKPath();
            clipPath.AddRoundRect(destRect, (float)_settings.BackgroundCornerRadius, (float)_settings.BackgroundCornerRadius);
            canvas.ClipPath(clipPath, SKClipOperation.Intersect, true);
        }
        else
        {
            canvas.ClipRect(destRect);
        }

        // Draw image to fill the entire clipped area seamlessly
        canvas.DrawImage(image, destRect, paint);
    }

    /// <summary>
    /// Renders background image in circular mode (Widget mode)
    /// </summary>
    private void RenderBackgroundImageCircular(SKCanvas canvas, SKImage image, SKPaint paint, int width, int height)
    {
        var textBounds = CalculateTextBounds();
        var radius = Math.Max(textBounds.Width, textBounds.Height) * 0.75f;

        var centerX = width / 2f + (float)_settings.BackgroundOffsetX;
        var centerY = height / 2f + (float)_settings.BackgroundOffsetY;

        // Create circular clipping path
        var clipPath = new SKPath();
        clipPath.AddCircle(centerX, centerY, radius);
        canvas.ClipPath(clipPath, SKClipOperation.Intersect, true);

        // Draw image to fill the circle
        var destRect = new SKRect(centerX - radius, centerY - radius, centerX + radius, centerY + radius);
        canvas.DrawImage(image, destRect, paint);
    }

    /// <summary>
    /// Renders background image in elliptical mode (Widget mode)
    /// </summary>
    private void RenderBackgroundImageElliptical(SKCanvas canvas, SKImage image, SKPaint paint, int width, int height)
    {
        var textBounds = CalculateTextBounds();
        var radiusX = textBounds.Width * 0.75f;
        var radiusY = textBounds.Height * 0.75f;

        var centerX = width / 2f + (float)_settings.BackgroundOffsetX;
        var centerY = height / 2f + (float)_settings.BackgroundOffsetY;

        // Create elliptical clipping path
        var clipPath = new SKPath();
        var rect = new SKRect(centerX - radiusX, centerY - radiusY, centerX + radiusX, centerY + radiusY);
        clipPath.AddOval(rect);
        canvas.ClipPath(clipPath, SKClipOperation.Intersect, true);

        // Draw image to fill the ellipse
        canvas.DrawImage(image, rect, paint);
    }

    /// <summary>
    /// Renders background image in rounded rectangle mode (Widget mode)
    /// </summary>
    private void RenderBackgroundImageRoundedRect(SKCanvas canvas, SKImage image, SKPaint paint, int width, int height)
    {
        var textBounds = CalculateTextBounds();

        float backgroundWidth = textBounds.Width * 1.25f;
        float backgroundHeight = textBounds.Height * 1.25f;

        var centerX = width / 2f + (float)_settings.BackgroundOffsetX;
        var centerY = height / 2f + (float)_settings.BackgroundOffsetY;

        var left = centerX - backgroundWidth / 2f;
        var top = centerY - backgroundHeight / 2f;
        var right = left + backgroundWidth;
        var bottom = top + backgroundHeight;

        var destRect = new SKRect(left, top, right, bottom);

        // Create rounded rectangle clipping path
        var clipPath = new SKPath();
        clipPath.AddRoundRect(destRect, (float)_settings.BackgroundCornerRadius, (float)_settings.BackgroundCornerRadius);
        canvas.ClipPath(clipPath, SKClipOperation.Intersect, true);

        // Draw image to fill the rounded rectangle
        canvas.DrawImage(image, destRect, paint);
    }

    private void DrawGlowEffect(SKCanvas canvas, string text, float x, float y, SKPaint basePaint)
    {
        try
        {
            // Create multiple layers for a proper glow effect
            var glowLayers = new[] { (float)_settings.GlowRadius * 1.5f, (float)_settings.GlowRadius, (float)_settings.GlowRadius * 0.7f, (float)_settings.GlowRadius * 0.4f };
            var glowOpacities = new[] { 0.2f, 0.3f, 0.5f, 0.7f };

            for (int i = 0; i < glowLayers.Length; i++)
            {
                using var glowPaint = basePaint.Clone();
                glowPaint.MaskFilter = SKMaskFilter.CreateBlur(SKBlurStyle.Normal, glowLayers[i]);
                glowPaint.Color = ToSKColor(_settings.GlowColor).WithAlpha((byte)(255 * glowOpacities[i]));
                canvas.DrawText(text, x, y, glowPaint);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error drawing glow effect: {ex.Message}");
        }
    }

    /// <summary>
    /// Loads custom fonts with enhanced validation and path resolution
    /// </summary>
    private void LoadCustomFonts()
    {
        try
        {
            var assetPaths = GetAssetFontPaths();
            var validatedCount = 0;

            foreach (var assetPath in assetPaths)
            {
                if (Directory.Exists(assetPath))
                {
                    System.Diagnostics.Debug.WriteLine($"Scanning font directory: {assetPath}");

                    var fontFiles = Directory.GetFiles(assetPath, "*.ttf", SearchOption.AllDirectories)
                        .Concat(Directory.GetFiles(assetPath, "*.otf", SearchOption.AllDirectories))
                        .Concat(Directory.GetFiles(assetPath, "*.ttc", SearchOption.AllDirectories));

                    foreach (var fontFile in fontFiles)
                    {
                        if (ValidateAndLoadAssetFont(fontFile))
                        {
                            validatedCount++;
                        }
                    }
                }
            }

            System.Diagnostics.Debug.WriteLine($"ClockWidget: Loaded {validatedCount} validated custom fonts");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error loading custom fonts in widget: {ex.Message}");
        }
    }

    /// <summary>
    /// Gets potential asset font paths in order of preference
    /// </summary>
    private List<string> GetAssetFontPaths()
    {
        var basePaths = new List<string>();
        var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;

        // Try multiple potential locations (same as ClockWorkshopViewModel)
        basePaths.Add(Path.Combine(baseDirectory, "..", "..", "..", "..", "assets", "Fonts"));
        basePaths.Add(Path.Combine(baseDirectory, "..", "..", "..", "assets", "Fonts"));
        basePaths.Add(Path.Combine(baseDirectory, "..", "..", "assets", "Fonts"));
        basePaths.Add(Path.Combine(baseDirectory, "..", "assets", "Fonts"));
        basePaths.Add(Path.Combine(baseDirectory, "assets", "Fonts"));
        basePaths.Add(Path.Combine(baseDirectory, "Assets", "Fonts"));
        basePaths.Add(Path.Combine(baseDirectory, "fonts"));
        basePaths.Add(Path.Combine(baseDirectory, "Fonts"));
        basePaths.Add(Path.Combine(baseDirectory, "..", "..", "..", "..", "modules", "assets", "Fonts"));
        basePaths.Add(Path.Combine(baseDirectory, "..", "..", "..", "modules", "assets", "Fonts"));

        return basePaths.Select(Path.GetFullPath).Distinct().ToList();
    }

    /// <summary>
    /// Validates and loads a single asset font file
    /// </summary>
    private bool ValidateAndLoadAssetFont(string fontFile)
    {
        SKData? fontData = null;
        SKTypeface? typeface = null;

        try
        {
            // Validate file exists and is readable
            if (!File.Exists(fontFile) || new FileInfo(fontFile).Length == 0)
            {
                return false;
            }

            // Load the font file and create typeface
            fontData = SKData.Create(fontFile);
            if (fontData == null) return false;

            typeface = SKTypeface.FromData(fontData);
            if (typeface == null) return false;

            // Test font rendering capability
            if (!TestFontRendering(typeface))
            {
                return false;
            }

            // Get font name
            var fontName = GetValidFontName(typeface, fontFile);
            if (string.IsNullOrWhiteSpace(fontName)) return false;

            // Check for duplicates
            if (_loadedFonts.ContainsKey(fontName))
            {
                return false;
            }

            // Successfully validated - add to collection
            _loadedFonts[fontName] = typeface;
            System.Diagnostics.Debug.WriteLine($"ClockWidget: Successfully loaded font: {fontName} from {fontFile}");
            return true;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"ClockWidget: Font validation failed for {fontFile}: {ex.Message}");
            typeface?.Dispose();
            return false;
        }
        finally
        {
            fontData?.Dispose();
        }
    }

    /// <summary>
    /// Tests font rendering capability by attempting to render test text
    /// Last Updated: 2025-01-11 18:45:00 UTC
    /// </summary>
    /// <param name="typeface">Typeface to test</param>
    /// <returns>True if font renders correctly</returns>
    private bool TestFontRendering(SKTypeface typeface)
    {
        var paint = _paintPool.Get();
        try
        {
            paint.Typeface = typeface;
            paint.TextSize = 24;
            paint.IsAntialias = true;

            // Test with common clock characters
            var testText = "0123456789:. AM PM";
            var bounds = new SKRect();
            var width = paint.MeasureText(testText, ref bounds);

            // Font is valid if it can measure text and has reasonable bounds
            return width > 0 && bounds.Width > 0 && bounds.Height > 0;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"ClockWidget: Font rendering test failed for typeface: {typeface.FamilyName} - {ex.Message}");
            return false;
        }
        finally
        {
            _paintPool.Return(paint);
        }
    }

    /// <summary>
    /// Gets a valid font name from typeface or filename
    /// </summary>
    private string GetValidFontName(SKTypeface typeface, string fontFile)
    {
        // Try to get the actual font family name from the typeface
        var fontName = typeface.FamilyName;

        // If that fails, use filename with cleanup
        if (string.IsNullOrWhiteSpace(fontName))
        {
            fontName = Path.GetFileNameWithoutExtension(fontFile);
        }

        return CleanFontName(fontName);
    }

    private string CleanFontName(string fontName)
    {
        // Clean up font name for better display
        fontName = fontName.Replace("_", " ").Replace("-", " ");

        // Remove common suffixes but be more selective
        var suffixes = new[] { " Regular", " Bold", " Italic", " Light", " Medium", " Thin",
                              " Black", " Heavy", " SemiBold", " ExtraBold", " (mono)", " (italic)" };

        foreach (var suffix in suffixes)
        {
            if (fontName.EndsWith(suffix, StringComparison.OrdinalIgnoreCase))
            {
                fontName = fontName.Substring(0, fontName.Length - suffix.Length);
                break; // Only remove one suffix
            }
        }

        // Capitalize first letter of each word
        return System.Globalization.CultureInfo.CurrentCulture.TextInfo.ToTitleCase(fontName.ToLower());
    }

    /// <summary>
    /// Gets a typeface using the enhanced font loading service with comprehensive fallback
    /// Last Updated: 2025-01-11 18:45:00 UTC
    /// </summary>
    /// <param name="fontFamily">Font family name</param>
    /// <returns>SKTypeface instance</returns>
    private SKTypeface GetTypeface(string fontFamily)
    {
        try
        {
            // Use enhanced font service for comprehensive font loading with fallback
            var typeface = _enhancedFontService.GetTypefaceAsync(fontFamily).GetAwaiter().GetResult();
            return typeface ?? SKTypeface.Default;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"ClockWidget: Error getting typeface for font '{fontFamily}', using default: {ex.Message}");
            return SKTypeface.Default;
        }
    }

    private void Draw3DText(SKCanvas canvas, string text, float x, float y, SKPaint basePaint)
    {
        try
        {
            var depth = (float)_settings.TextDepth;
            var bevel = (float)_settings.BevelSize;

            // Create 3D effect by drawing multiple layers
            for (int i = (int)depth; i >= 0; i--)
            {
                using var layerPaint = basePaint.Clone();

                // Calculate depth color (darker as we go deeper)
                var depthFactor = 1.0f - (i / depth * 0.6f); // Darken by up to 60%
                var baseColor = ToSKColor(_settings.Text3DColor);

                // Apply lighting if enabled
                if (_settings.EnableLighting)
                {
                    depthFactor = ApplyLighting(depthFactor, i, depth);
                }

                layerPaint.Color = new SKColor(
                    (byte)(baseColor.Red * depthFactor),
                    (byte)(baseColor.Green * depthFactor),
                    (byte)(baseColor.Blue * depthFactor),
                    baseColor.Alpha);

                // Draw the layer with offset
                var offsetX = x + (i * 0.5f);
                var offsetY = y + (i * 0.5f);

                // Add bevel effect for front layers
                if (i <= bevel)
                {
                    var bevelFactor = 1.0f + (bevel - i) * 0.2f;
                    layerPaint.Color = layerPaint.Color.WithAlpha((byte)(255 * bevelFactor));
                }

                canvas.DrawText(text, offsetX, offsetY, layerPaint);
            }

            // Draw the front face
            using var frontPaint = basePaint.Clone();
            frontPaint.Color = ToSKColor(_settings.Text3DColor);
            canvas.DrawText(text, x, y, frontPaint);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error drawing 3D text: {ex.Message}");
        }
    }

    private float ApplyLighting(float baseFactor, int layer, float totalDepth)
    {
        try
        {
            // Convert angles to radians
            var angleXRad = _settings.LightAngleX * Math.PI / 180.0;
            var angleYRad = _settings.LightAngleY * Math.PI / 180.0;

            // Calculate light direction
            var lightX = Math.Cos(angleXRad);
            var lightY = Math.Sin(angleYRad);
            var lightZ = Math.Sin(angleXRad);

            // Calculate surface normal (simplified)
            var normalX = 0.0;
            var normalY = 0.0;
            var normalZ = 1.0;

            // Calculate dot product for lighting intensity
            var dotProduct = lightX * normalX + lightY * normalY + lightZ * normalZ;
            var lightingFactor = Math.Max(0.2, dotProduct) * _settings.LightIntensity;

            // Apply depth-based lighting variation
            var depthFactor = 1.0f - (layer / totalDepth * 0.3f);

            return (float)(baseFactor * lightingFactor * depthFactor);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error applying lighting: {ex.Message}");
            return baseFactor;
        }
    }

    /// <summary>
    /// Gets enhanced text color for better visibility in transparent mode
    /// </summary>
    private SKColor GetEnhancedTextColor(WpfBrush brush)
    {
        var baseColor = ToSKColor(brush);

        if (_settings.TransparentBackground)
        {
            // Ensure text has sufficient opacity and contrast for transparent backgrounds
            var alpha = Math.Max(baseColor.Alpha, (byte)220); // Minimum 86% opacity
            return baseColor.WithAlpha(alpha);
        }

        return baseColor;
    }

    /// <summary>
    /// Gets enhanced shadow color for better visibility in transparent mode
    /// </summary>
    private SKColor GetEnhancedShadowColor(WpfBrush brush)
    {
        var baseColor = ToSKColor(brush);

        if (_settings.TransparentBackground)
        {
            // Enhance shadow opacity for better text definition
            var alpha = Math.Max(baseColor.Alpha, (byte)180); // Minimum 70% opacity
            return baseColor.WithAlpha(alpha);
        }

        return baseColor;
    }

    /// <summary>
    /// Gets enhanced stroke color for better visibility in transparent mode
    /// </summary>
    private SKColor GetEnhancedStrokeColor(WpfBrush brush)
    {
        var baseColor = ToSKColor(brush);

        if (_settings.TransparentBackground)
        {
            // Enhance stroke opacity for better text definition
            var alpha = Math.Max(baseColor.Alpha, (byte)200); // Minimum 78% opacity
            return baseColor.WithAlpha(alpha);
        }

        return baseColor;
    }

    private static SKColor ToSKColor(WpfBrush brush)
    {
        if (brush is SolidColorBrush solidBrush)
        {
            var color = solidBrush.Color;
            return new SKColor(color.R, color.G, color.B, color.A);
        }
        return SKColors.White;
    }
}
