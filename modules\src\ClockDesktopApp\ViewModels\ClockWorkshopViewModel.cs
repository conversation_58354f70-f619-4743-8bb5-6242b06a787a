using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Media;
using System.Windows.Threading;
using ArtDesignFramework.ClockDesktopApp.Models;
using ArtDesignFramework.ClockDesktopApp.Services;
using ArtDesignFramework.Core;
using ArtDesignFramework.Core.AI;
using ArtDesignFramework.UserInterface.Services.Rendering;
using ArtDesignFramework.UserInterface.Tools;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using Microsoft.Win32;
using SkiaSharp;

namespace ArtDesignFramework.ClockDesktopApp.ViewModels;

/// <summary>
/// Represents progress information for font loading operations
/// </summary>
public class FontLoadingProgress
{
    public int CurrentStep { get; }
    public int TotalSteps { get; }
    public string Message { get; }
    public double PercentComplete => TotalSteps > 0 ? (double)CurrentStep / TotalSteps * 100 : 0;

    public FontLoadingProgress(int currentStep, int totalSteps, string message)
    {
        CurrentStep = currentStep;
        TotalSteps = totalSteps;
        Message = message ?? string.Empty;
    }
}

public partial class ClockWorkshopViewModel : ObservableObject, IDisposable
{
    private readonly ILogger<ClockWorkshopViewModel> _logger;
    private readonly ClockWidgetService _widgetService;
    private readonly SettingsService _settingsService;
    private readonly DateComponentManager _dateComponentManager;
    private readonly DragDropPreviewService _dragDropService;
    private readonly ClockLayerManager _layerManager;
    private readonly IEnhanced3DTextRenderer _enhanced3DRenderer;
    private readonly ISKPaintPool _paintPool;
    private readonly SelectionToolsEngine _selectionToolsEngine;
    private readonly IAIEngine _aiEngine;
    private DispatcherTimer? _previewUpdateTimer;

    public event Action? PreviewUpdateRequested;

    public ClockWorkshopViewModel(
        ILogger<ClockWorkshopViewModel> logger,
        ClockWidgetService widgetService,
        SettingsService settingsService,
        DateComponentManager dateComponentManager,
        DragDropPreviewService dragDropService,
        ClockLayerManager layerManager,
        IEnhanced3DTextRenderer enhanced3DRenderer,
        ISKPaintPool paintPool,
        SelectionToolsEngine selectionToolsEngine,
        IAIEngine aiEngine)
    {
        _logger = logger;
        _widgetService = widgetService;
        _settingsService = settingsService;
        _dateComponentManager = dateComponentManager;
        _dragDropService = dragDropService;
        _layerManager = layerManager;
        _enhanced3DRenderer = enhanced3DRenderer;
        _paintPool = paintPool ?? throw new ArgumentNullException(nameof(paintPool));
        _selectionToolsEngine = selectionToolsEngine ?? throw new ArgumentNullException(nameof(selectionToolsEngine));
        _aiEngine = aiEngine ?? throw new ArgumentNullException(nameof(aiEngine));

        // Initialize collections
        AvailableFonts = new ObservableCollection<string>();

        // Start font loading asynchronously without blocking the constructor
        _ = Task.Run(async () =>
        {
            try
            {
                await LoadAvailableFontsAsync();

                // Update UI on the UI thread
                System.Windows.Application.Current?.Dispatcher.Invoke(() =>
                {
                    OnPropertyChanged(nameof(AvailableFonts));
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during background font loading");
            }
        });

        AvailableShapes = new ObservableCollection<string>
        {
            "Rectangle", "RoundedRectangle", "Circle", "Ellipse"
        };

        // Initialize date format presets like the samples in the image
        DateFormatPresets = new ObservableCollection<string>
        {
            "dddd, MMMM d, yyyy",           // Monday, September 12th, 2025
            "MM/dd | HH:mm:ss",             // 01/12 | 07:24:31
            "ddd MMM d",                    // Fri Jan 10
            "MM.dd.yy",                     // 01.12.25
            "HH:mm:ss",                     // 07:24:31
            "hh:mm tt",                     // 12:34 AM
            "HH:mm",                        // 12:34
            "MMM d, yyyy",                  // Jan 12, 2025
            "dd/MM/yyyy",                   // 12/01/2025
            "yyyy-MM-dd",                   // 2025-01-12
            "MMMM d",                       // January 12
            "ddd, MMM d",                   // Mon, Jan 12
            "HH:mm | dd.MM",                // 12:34 | 12.01
            "mm:ss",                        // 34:56 (minutes:seconds)
            "tt",                           // AM/PM only
            "dddd",                         // Monday (day only)
            "MMMM yyyy"                     // January 2025
        };

        // Initialize enhanced 3D material options
        AvailableMaterials = new ObservableCollection<SurfaceMaterial>
        {
            SurfaceMaterial.Matte,
            SurfaceMaterial.Glossy,
            SurfaceMaterial.Metallic,
            SurfaceMaterial.Chrome,
            SurfaceMaterial.Gold,
            SurfaceMaterial.Copper,
            SurfaceMaterial.Glass,
            SurfaceMaterial.Plastic,
            SurfaceMaterial.Rubber
        };

        // Initialize light type options
        AvailableLightTypes = new ObservableCollection<LightType>
        {
            LightType.Directional,
            LightType.Point,
            LightType.Spot,
            LightType.Area
        };

        // Initialize lighting preset options
        AvailableLightingPresets = new ObservableCollection<LightingPreset>
        {
            LightingPreset.Custom,
            LightingPreset.Studio,
            LightingPreset.Natural,
            LightingPreset.Dramatic,
            LightingPreset.Soft,
            LightingPreset.Cinematic,
            LightingPreset.Neon,
            LightingPreset.Sunset,
            LightingPreset.Moonlight,
            LightingPreset.HighKey,
            LightingPreset.LowKey
        };

        // Initialize light blending mode options
        AvailableLightBlendingModes = new ObservableCollection<LightBlendingMode>
        {
            Services.LightBlendingMode.Additive,
            Services.LightBlendingMode.Multiplicative,
            Services.LightBlendingMode.Screen,
            Services.LightBlendingMode.Overlay
        };

        // Initialize light priority options
        AvailableLightPriorities = new ObservableCollection<LightPriority>
        {
            Services.LightPriority.Low,
            Services.LightPriority.Normal,
            Services.LightPriority.High,
            Services.LightPriority.Critical
        };

        // Initialize blend mode options
        AvailableBlendModes = new ObservableCollection<ClockBlendMode>
        {
            ClockBlendMode.Normal,
            ClockBlendMode.Multiply,
            ClockBlendMode.Screen,
            ClockBlendMode.Overlay,
            ClockBlendMode.SoftLight,
            ClockBlendMode.HardLight,
            ClockBlendMode.ColorDodge,
            ClockBlendMode.ColorBurn,
            ClockBlendMode.Darken,
            ClockBlendMode.Lighten,
            ClockBlendMode.Difference,
            ClockBlendMode.Exclusion
        };

        // Set defaults with better contrast
        SelectedFont = "Arial";
        FontSize = 94; // Increased by 30% from 72 for better readability
        TextColor = System.Windows.Media.Brushes.White;
        DateColor = System.Windows.Media.Brushes.LightGray;
        BackgroundColor = System.Windows.Media.Brushes.DarkBlue; // Better contrast than black
        ClockWidth = 800;
        ClockHeight = 600;
        PreviewScale = 100;

        // Load saved settings
        LoadSettings();

        // Set up preview update timer
        SetupPreviewTimer();

        // Initialize drag-drop service
        InitializeDragDropService();

        // Initialize layer manager events
        InitializeLayerManagerEvents();
    }

    // Properties for UI binding
    [ObservableProperty]
    private bool alwaysOnTop;

    [ObservableProperty]
    private bool showDate = true;

    [ObservableProperty]
    private bool showAmPm = true;

    [ObservableProperty]
    private bool use24Hour;

    [ObservableProperty]
    private string selectedFont = "Arial";

    [ObservableProperty]
    private double fontSize = 94; // Increased default for better readability

    [ObservableProperty]
    private System.Windows.Media.Brush textColor = System.Windows.Media.Brushes.White;

    [ObservableProperty]
    private System.Windows.Media.Brush dateColor = System.Windows.Media.Brushes.LightGray;

    // Advanced Date Properties
    [ObservableProperty]
    private string dateFormat = "dddd, MMMM d, yyyy";

    [ObservableProperty]
    private double dateFontSize = 16;

    [ObservableProperty]
    private string dateFontFamily = "Arial";

    [ObservableProperty]
    private double dateOffsetX = 0;

    [ObservableProperty]
    private double dateOffsetY = 30;

    // Time Format Options
    [ObservableProperty]
    private bool showSeconds;

    [ObservableProperty]
    private string timeFormat = "HH:mm";

    [ObservableProperty]
    private string secondsFormat = ":ss";

    // AM/PM Properties (separate layer)
    [ObservableProperty]
    private bool showAmPmSeparate;

    [ObservableProperty]
    private System.Windows.Media.Brush amPmColor = System.Windows.Media.Brushes.LightGray;

    [ObservableProperty]
    private double amPmFontSize = 12;

    [ObservableProperty]
    private string amPmFontFamily = "Arial";

    [ObservableProperty]
    private double amPmOffsetX = 0;

    [ObservableProperty]
    private double amPmOffsetY = -10;

    // Advanced Date Formats
    [ObservableProperty]
    private bool useCustomDateFormat;

    [ObservableProperty]
    private string customDateFormat = "MM/dd | HH:mm:ss";

    // Granular Date Component Controls
    [ObservableProperty]
    private bool showDay = true;

    [ObservableProperty]
    private bool showMonth = true;

    [ObservableProperty]
    private bool showYear = true;

    // Individual Date Component Positioning
    [ObservableProperty]
    private double dayOffsetX = 0;

    [ObservableProperty]
    private double dayOffsetY = 0;

    [ObservableProperty]
    private double monthOffsetX = 0;

    [ObservableProperty]
    private double monthOffsetY = 0;

    [ObservableProperty]
    private double yearOffsetX = 0;

    [ObservableProperty]
    private double yearOffsetY = 0;

    // Individual Date Component Font Sizes
    [ObservableProperty]
    private double dayFontSize = 48;

    [ObservableProperty]
    private double monthFontSize = 48;

    [ObservableProperty]
    private double yearFontSize = 48;

    // Drag and Drop Properties
    [ObservableProperty]
    private bool isDragModeEnabled = false;

    [ObservableProperty]
    private bool snapToGridEnabled = true;

    [ObservableProperty]
    private double gridSize = 5.0;

    [ObservableProperty]
    private bool showGrid = false;

    // Advanced Layer Management Properties
    [ObservableProperty]
    private bool advancedModeEnabled = false;

    [ObservableProperty]
    private bool layerPanelVisible = false;

    [ObservableProperty]
    private ClockLayer? selectedLayer = null;

    [ObservableProperty]
    private bool layerLocked = false;

    [ObservableProperty]
    private double layerRotation = 0.0;

    [ObservableProperty]
    private double layerScaleX = 1.0;

    [ObservableProperty]
    private double layerScaleY = 1.0;

    [ObservableProperty]
    private double layerSkewX = 0.0;

    [ObservableProperty]
    private double layerSkewY = 0.0;

    [ObservableProperty]
    private ClockBlendMode layerBlendMode = ClockBlendMode.Normal;

    // Opacity Controls
    [ObservableProperty]
    private double textOpacity = 1.0;

    [ObservableProperty]
    private double strokeOpacity = 1.0;

    [ObservableProperty]
    private double dateOpacity = 1.0;

    [ObservableProperty]
    private bool transparentBackground;

    [ObservableProperty]
    private System.Windows.Media.Brush backgroundColor = System.Windows.Media.Brushes.Black;

    [ObservableProperty]
    private string? backgroundImagePath;

    public bool HasBackgroundImage => !string.IsNullOrEmpty(BackgroundImagePath);

    // Background Shape Properties
    [ObservableProperty]
    private string backgroundShape = "Rectangle"; // Rectangle, RoundedRectangle, Circle, Ellipse

    [ObservableProperty]
    private double backgroundCornerRadius = 0;

    [ObservableProperty]
    private double backgroundFeather = 0; // Blur/feather effect

    [ObservableProperty]
    private double backgroundPadding = 5; // Much smaller default padding

    // Enhanced Background Sizing and Positioning
    [ObservableProperty]
    private double backgroundWidth = 0; // 0 = auto-calculate as 25% larger than text

    [ObservableProperty]
    private double backgroundHeight = 0; // 0 = auto-calculate as 25% larger than text

    [ObservableProperty]
    private double backgroundOffsetX = 0; // X offset for background positioning

    [ObservableProperty]
    private double backgroundOffsetY = 0; // Y offset for background positioning

    [ObservableProperty]
    private bool enableGlow;

    [ObservableProperty]
    private System.Windows.Media.Brush glowColor = System.Windows.Media.Brushes.White;

    [ObservableProperty]
    private double glowRadius = 10;

    [ObservableProperty]
    private bool enableShadow;

    [ObservableProperty]
    private System.Windows.Media.Brush shadowColor = System.Windows.Media.Brushes.Black;

    [ObservableProperty]
    private double shadowOffsetX = 3;

    [ObservableProperty]
    private double shadowOffsetY = 3;

    [ObservableProperty]
    private double shadowBlur = 5;

    [ObservableProperty]
    private bool enableStroke;

    [ObservableProperty]
    private System.Windows.Media.Brush strokeColor = System.Windows.Media.Brushes.White;

    [ObservableProperty]
    private double strokeWidth = 2;

    // 3D Text Properties
    [ObservableProperty]
    private bool enable3D;

    [ObservableProperty]
    private double textDepth = 15;

    [ObservableProperty]
    private double bevelSize = 2;

    [ObservableProperty]
    private System.Windows.Media.Brush text3DColor = System.Windows.Media.Brushes.Silver;

    // Enhanced 3D and Lighting Properties
    [ObservableProperty]
    private bool enableLighting;

    [ObservableProperty]
    private System.Windows.Media.Brush lightColor = System.Windows.Media.Brushes.White;

    [ObservableProperty]
    private double lightIntensity = 1.0;

    [ObservableProperty]
    private double lightAngleX = 45;

    [ObservableProperty]
    private double lightAngleY = -45;

    // Surface Material Properties
    [ObservableProperty]
    private SurfaceMaterial material = SurfaceMaterial.Matte;

    [ObservableProperty]
    private double metallic = 0.0;

    [ObservableProperty]
    private double roughness = 0.5;

    [ObservableProperty]
    private double reflectance = 0.04;

    // Multiple Light Sources
    [ObservableProperty]
    private bool enableMultipleLights = false;

    [ObservableProperty]
    private System.Windows.Media.Brush secondaryLightColor = System.Windows.Media.Brushes.LightBlue;

    [ObservableProperty]
    private double secondaryLightIntensity = 0.5;

    [ObservableProperty]
    private double secondaryLightAngleX = -45;

    [ObservableProperty]
    private double secondaryLightAngleY = 45;

    [ObservableProperty]
    private LightType secondaryLightType = LightType.Directional;

    // Tertiary Light Source
    [ObservableProperty]
    private bool enableTertiaryLight = false;

    [ObservableProperty]
    private System.Windows.Media.Brush tertiaryLightColor = System.Windows.Media.Brushes.Orange;

    [ObservableProperty]
    private double tertiaryLightIntensity = 0.3;

    [ObservableProperty]
    private double tertiaryLightAngleX = 90;

    [ObservableProperty]
    private double tertiaryLightAngleY = 0;

    [ObservableProperty]
    private LightType tertiaryLightType = LightType.Point;

    // Quaternary Light Source
    [ObservableProperty]
    private bool enableQuaternaryLight = false;

    [ObservableProperty]
    private System.Windows.Media.Brush quaternaryLightColor = System.Windows.Media.Brushes.Purple;

    [ObservableProperty]
    private double quaternaryLightIntensity = 0.2;

    [ObservableProperty]
    private double quaternaryLightAngleX = -90;

    [ObservableProperty]
    private double quaternaryLightAngleY = 0;

    [ObservableProperty]
    private LightType quaternaryLightType = LightType.Spot;

    // Light Source Management
    [ObservableProperty]
    private LightingPreset lightingPreset = LightingPreset.Custom;

    [ObservableProperty]
    private LightBlendingMode blendingMode = LightBlendingMode.Multiplicative;

    [ObservableProperty]
    private double legacyLightBlendingMode = 1.0;

    [ObservableProperty]
    private bool enableLightPriority = true;

    [ObservableProperty]
    private double lightFalloffDistance = 100.0;

    // Light Priority Settings
    [ObservableProperty]
    private LightPriority primaryLightPriority = LightPriority.Critical;

    [ObservableProperty]
    private LightPriority secondaryLightPriority = LightPriority.High;

    [ObservableProperty]
    private LightPriority tertiaryLightPriority = LightPriority.Normal;

    [ObservableProperty]
    private LightPriority quaternaryLightPriority = LightPriority.Low;

    // Advanced Light Properties
    [ObservableProperty]
    private double spotLightConeAngle = 30.0;

    [ObservableProperty]
    private double spotLightPenumbraAngle = 5.0;

    [ObservableProperty]
    private double areaLightWidth = 50.0;

    [ObservableProperty]
    private double areaLightHeight = 50.0;

    [ObservableProperty]
    private bool enableAdvancedLightFalloff = true;

    // Ambient Occlusion
    [ObservableProperty]
    private bool enableAmbientOcclusion = false;

    [ObservableProperty]
    private double ambientOcclusionIntensity = 0.3;

    [ObservableProperty]
    private double ambientOcclusionRadius = 5.0;

    [ObservableProperty]
    private System.Windows.Media.Brush ambientColor = System.Windows.Media.Brushes.Gray;

    [ObservableProperty]
    private double ambientIntensity = 0.2;

    // Environment Reflection
    [ObservableProperty]
    private bool enableEnvironmentReflection = false;

    [ObservableProperty]
    private double reflectionIntensity = 0.3;

    [ObservableProperty]
    private string? environmentMapPath;

    // Advanced Lighting
    [ObservableProperty]
    private bool enableSoftShadows = true;

    [ObservableProperty]
    private double shadowSoftness = 2.0;

    [ObservableProperty]
    private bool enableGlobalIllumination = false;

    [ObservableProperty]
    private int maxLightBounces = 2;

    // Advanced Text Properties
    [ObservableProperty]
    private bool enableOutline;

    [ObservableProperty]
    private System.Windows.Media.Brush outlineColor = System.Windows.Media.Brushes.Black;

    [ObservableProperty]
    private double outlineWidth = 1;

    [ObservableProperty]
    private bool enableInnerShadow;

    [ObservableProperty]
    private System.Windows.Media.Brush innerShadowColor = System.Windows.Media.Brushes.Black;

    [ObservableProperty]
    private double innerShadowOffsetX = 2;

    [ObservableProperty]
    private double innerShadowOffsetY = 2;

    [ObservableProperty]
    private double innerShadowBlur = 3;

    [ObservableProperty]
    private bool enableGradient;

    [ObservableProperty]
    private System.Windows.Media.Brush gradientStartColor = System.Windows.Media.Brushes.White;

    [ObservableProperty]
    private System.Windows.Media.Brush gradientEndColor = System.Windows.Media.Brushes.Gray;

    [ObservableProperty]
    private double gradientAngle = 90;

    // Advanced Background Properties
    [ObservableProperty]
    private bool enableBackgroundGradient;

    [ObservableProperty]
    private System.Windows.Media.Brush backgroundGradientStart = System.Windows.Media.Brushes.DarkBlue;

    [ObservableProperty]
    private System.Windows.Media.Brush backgroundGradientEnd = System.Windows.Media.Brushes.LightBlue;

    [ObservableProperty]
    private double backgroundGradientAngle = 45;

    [ObservableProperty]
    private bool enableBackgroundBorder;

    [ObservableProperty]
    private System.Windows.Media.Brush backgroundBorderColor = System.Windows.Media.Brushes.White;

    [ObservableProperty]
    private double backgroundBorderWidth = 2;

    // Typography Properties
    [ObservableProperty]
    private double letterSpacing = 0;

    [ObservableProperty]
    private double lineHeight = 1.0;

    [ObservableProperty]
    private double textSkewX = 0;

    [ObservableProperty]
    private double textSkewY = 0;

    [ObservableProperty]
    private double previewScale = 100;

    [ObservableProperty]
    private int clockWidth = 800;

    [ObservableProperty]
    private int clockHeight = 600;

    // Selection Tools Integration Properties
    [ObservableProperty]
    private bool selectionToolsEnabled = false;

    [ObservableProperty]
    private SelectionType selectedSelectionTool = SelectionType.Rectangle;

    [ObservableProperty]
    private bool eyeDropperEnabled = false;

    [ObservableProperty]
    private bool desktopColorSamplingEnabled = false;

    [ObservableProperty]
    private System.Windows.Media.Brush sampledColor = System.Windows.Media.Brushes.White;

    // AI-Powered Customization Properties
    [ObservableProperty]
    private bool aiSuggestionsEnabled = true;

    [ObservableProperty]
    private bool autoApplyAISuggestions = false;

    [ObservableProperty]
    private double aiConfidenceThreshold = 0.7;

    [ObservableProperty]
    private string aiSuggestionStatus = "Ready";

    [ObservableProperty]
    private ObservableCollection<UISuggestion> currentAISuggestions = new();

    [ObservableProperty]
    private ObservableCollection<BrushRecommendation> currentBrushRecommendations = new();

    public ObservableCollection<string> AvailableFonts { get; }
    public ObservableCollection<string> AvailableShapes { get; }
    public ObservableCollection<string> DateFormatPresets { get; }
    public ObservableCollection<SurfaceMaterial> AvailableMaterials { get; }
    public ObservableCollection<LightType> AvailableLightTypes { get; }
    public ObservableCollection<LightingPreset> AvailableLightingPresets { get; }
    public ObservableCollection<LightBlendingMode> AvailableLightBlendingModes { get; }
    public ObservableCollection<LightPriority> AvailableLightPriorities { get; }
    public ObservableCollection<ClockBlendMode> AvailableBlendModes { get; }

    /// <summary>
    /// Gets the clock layers from the layer manager
    /// </summary>
    public IReadOnlyList<ClockLayer> ClockLayers => _layerManager.Layers;

    // Commands
    [RelayCommand]
    private async Task CreateWidget()
    {
        try
        {
            _logger.LogInformation("Creating desktop widget...");

            var settings = CreateClockSettings();
            await _widgetService.CreateDesktopWidgetAsync(settings);

            _logger.LogInformation("Desktop widget created successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create desktop widget");
        }
    }

    [RelayCommand]
    private async Task SaveDesign()
    {
        try
        {
            var settings = CreateClockSettings();
            await _settingsService.SaveDesignAsync(settings);
            _logger.LogInformation("Design saved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save design");
        }
    }

    [RelayCommand]
    private async Task LoadDesign()
    {
        try
        {
            var settings = await _settingsService.LoadDesignAsync();
            if (settings != null)
            {
                ApplySettings(settings);
                _logger.LogInformation("Design loaded successfully");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load design");
        }
    }

    [RelayCommand]
    private async Task ResetToDefaults()
    {
        try
        {
            _logger.LogInformation("Resetting to factory defaults...");

            // Show confirmation dialog
            var result = System.Windows.MessageBox.Show(
                "Reset all settings to factory defaults?\n\nThis will restore the original clock appearance and cannot be undone.",
                "Reset to Factory Defaults",
                System.Windows.MessageBoxButton.YesNo,
                System.Windows.MessageBoxImage.Question,
                System.Windows.MessageBoxResult.No);

            if (result == System.Windows.MessageBoxResult.Yes)
            {
                // Create factory defaults (preserve position)
                var factoryDefaults = ClockSettings.CreateFactoryDefaults(preservePosition: true);

                // Load factory defaults into the view model
                ApplySettings(factoryDefaults);

                // Save the reset settings
                await _settingsService.SaveDesignAsync(factoryDefaults);

                // Update any active widgets
                _widgetService.UpdateAllWidgets(factoryDefaults);

                _logger.LogInformation("Successfully reset to factory defaults");

                // Show success message
                System.Windows.MessageBox.Show(
                    "Settings have been reset to factory defaults.",
                    "Reset Complete",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Information);
            }
            else
            {
                _logger.LogInformation("Reset to defaults cancelled by user");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to reset to factory defaults");

            // Show error message
            System.Windows.MessageBox.Show(
                "Failed to reset settings to factory defaults. Please try again.",
                "Reset Failed",
                System.Windows.MessageBoxButton.OK,
                System.Windows.MessageBoxImage.Error);
        }
    }

    [RelayCommand]
    private void ChooseTextColor()
    {
        var color = ShowColorPicker(TextColor);
        if (color != null)
        {
            TextColor = color;
            _logger.LogInformation("Text color changed to {Color}", color);
        }
    }

    [RelayCommand]
    private void ChooseDateColor()
    {
        var color = ShowColorPicker(DateColor);
        if (color != null)
        {
            DateColor = color;
            _logger.LogInformation("Date color changed to {Color}", color);
        }
    }

    [RelayCommand]
    private void ChooseAmPmColor()
    {
        var color = ShowColorPicker(AmPmColor);
        if (color != null)
        {
            AmPmColor = color;
            _logger.LogInformation("AM/PM color changed to {Color}", color);
        }
    }

    [RelayCommand]
    private void ChooseBackgroundColor()
    {
        var color = ShowColorPicker(BackgroundColor);
        if (color != null)
        {
            BackgroundColor = color;
            _logger.LogInformation("Background color changed to {Color}", color);
        }
    }

    [RelayCommand]
    private void ChooseGlowColor()
    {
        var color = ShowColorPicker(GlowColor);
        if (color != null)
        {
            GlowColor = color;
            _logger.LogInformation("Glow color changed to {Color}", color);
        }
    }

    [RelayCommand]
    private void ChooseShadowColor()
    {
        var color = ShowColorPicker(ShadowColor);
        if (color != null)
        {
            ShadowColor = color;
            _logger.LogInformation("Shadow color changed to {Color}", color);
        }
    }

    [RelayCommand]
    private void ChooseStrokeColor()
    {
        var color = ShowColorPicker(StrokeColor);
        if (color != null)
        {
            StrokeColor = color;
            _logger.LogInformation("Stroke color changed to {Color}", color);
        }
    }

    [RelayCommand]
    private void ChooseText3DColor()
    {
        var color = ShowColorPicker(Text3DColor);
        if (color != null)
        {
            Text3DColor = color;
            _logger.LogInformation("3D text color changed to {Color}", color);
        }
    }

    [RelayCommand]
    private void ChooseLightColor()
    {
        var color = ShowColorPicker(LightColor);
        if (color != null)
        {
            LightColor = color;
            _logger.LogInformation("Light color changed to {Color}", color);
        }
    }

    [RelayCommand]
    private void ChooseOutlineColor()
    {
        var color = ShowColorPicker(OutlineColor);
        if (color != null)
        {
            OutlineColor = color;
            _logger.LogInformation("Outline color changed to {Color}", color);
        }
    }

    [RelayCommand]
    private void ChooseInnerShadowColor()
    {
        var color = ShowColorPicker(InnerShadowColor);
        if (color != null)
        {
            InnerShadowColor = color;
            _logger.LogInformation("Inner shadow color changed to {Color}", color);
        }
    }

    [RelayCommand]
    private void ChooseGradientStartColor()
    {
        var color = ShowColorPicker(GradientStartColor);
        if (color != null)
        {
            GradientStartColor = color;
            _logger.LogInformation("Gradient start color changed to {Color}", color);
        }
    }

    [RelayCommand]
    private void ChooseGradientEndColor()
    {
        var color = ShowColorPicker(GradientEndColor);
        if (color != null)
        {
            GradientEndColor = color;
            _logger.LogInformation("Gradient end color changed to {Color}", color);
        }
    }

    [RelayCommand]
    private void ChooseBackgroundGradientStart()
    {
        var color = ShowColorPicker(BackgroundGradientStart);
        if (color != null)
        {
            BackgroundGradientStart = color;
            _logger.LogInformation("Background gradient start color changed to {Color}", color);
        }
    }

    [RelayCommand]
    private void ChooseBackgroundGradientEnd()
    {
        var color = ShowColorPicker(BackgroundGradientEnd);
        if (color != null)
        {
            BackgroundGradientEnd = color;
            _logger.LogInformation("Background gradient end color changed to {Color}", color);
        }
    }

    [RelayCommand]
    private void ChooseBackgroundBorderColor()
    {
        var color = ShowColorPicker(BackgroundBorderColor);
        if (color != null)
        {
            BackgroundBorderColor = color;
            _logger.LogInformation("Background border color changed to {Color}", color);
        }
    }

    // Selection Tools Integration Commands
    /// <summary>
    /// Toggles the selection tools functionality for advanced text positioning
    /// Last Updated: 2025-01-09 23:50:00 UTC
    /// </summary>
    [RelayCommand]
    private async Task ToggleSelectionTools()
    {
        try
        {
            SelectionToolsEnabled = !SelectionToolsEnabled;
            _logger.LogInformation("Selection tools {Status}", SelectionToolsEnabled ? "enabled" : "disabled");

            if (SelectionToolsEnabled)
            {
                // Initialize selection tools engine if needed
                await InitializeSelectionToolsAsync();
            }
            else
            {
                // Clear any active selections
                _selectionToolsEngine.ClearSelection();
            }

            PreviewUpdateRequested?.Invoke();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error toggling selection tools");
            SelectionToolsEnabled = false;
        }
    }

    /// <summary>
    /// Activates the eye dropper tool for color sampling from desktop wallpapers
    /// Last Updated: 2025-01-09 23:50:00 UTC
    /// </summary>
    [RelayCommand]
    private async Task ActivateEyeDropper()
    {
        try
        {
            EyeDropperEnabled = true;
            _logger.LogInformation("Eye dropper activated for desktop color sampling");

            // Start desktop color sampling
            var sampledColor = await SampleDesktopColorAsync();
            if (sampledColor != null)
            {
                SampledColor = sampledColor;
                _logger.LogInformation("Desktop color sampled: {Color}", sampledColor);

                // Optionally apply to current color selection
                if (DesktopColorSamplingEnabled)
                {
                    ApplySampledColorToCurrentSelection();
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating eye dropper");
        }
        finally
        {
            EyeDropperEnabled = false;
        }
    }

    /// <summary>
    /// Generates AI-powered customization suggestions for the current clock design
    /// Last Updated: 2025-01-09 23:50:00 UTC
    /// </summary>
    [RelayCommand]
    private async Task GenerateAISuggestions()
    {
        try
        {
            if (!AiSuggestionsEnabled)
            {
                _logger.LogWarning("AI suggestions are disabled");
                return;
            }

            AiSuggestionStatus = "Analyzing clock design...";
            _logger.LogInformation("Generating AI-powered customization suggestions");

            // Create canvas analysis data from current clock settings
            var canvasData = CreateCanvasAnalysisData();

            // Generate UI suggestions
            var uiSuggestions = await _aiEngine.GenerateUISuggestionsAsync();
            var canvasSuggestions = await _aiEngine.GenerateCanvasOptimizationSuggestionsAsync(canvasData);
            var brushRecommendations = await _aiEngine.GenerateBrushRecommendationsAsync(canvasData);

            // Update collections
            CurrentAISuggestions.Clear();
            foreach (var suggestion in uiSuggestions)
            {
                CurrentAISuggestions.Add(suggestion);
            }

            CurrentBrushRecommendations.Clear();
            foreach (var recommendation in brushRecommendations)
            {
                CurrentBrushRecommendations.Add(recommendation);
            }

            AiSuggestionStatus = $"Generated {uiSuggestions.Count} suggestions";
            _logger.LogInformation("Generated {Count} AI suggestions", uiSuggestions.Count);

            // Auto-apply high-confidence suggestions if enabled
            if (AutoApplyAISuggestions)
            {
                await ApplyHighConfidenceSuggestionsAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating AI suggestions");
            AiSuggestionStatus = "Error generating suggestions";
        }
    }

    /// <summary>
    /// Applies a specific AI suggestion to the clock design
    /// Last Updated: 2025-01-09 23:50:00 UTC
    /// </summary>
    [RelayCommand]
    private async Task ApplyAISuggestion(UISuggestion suggestion)
    {
        try
        {
            if (suggestion == null)
            {
                _logger.LogWarning("Cannot apply null AI suggestion");
                return;
            }

            _logger.LogInformation("Applying AI suggestion: {Type}", suggestion.SuggestionType);

            switch (suggestion.SuggestionType)
            {
                case "Canvas Selection Optimization":
                    ApplySelectionOptimizationSuggestion(suggestion);
                    break;

                case "Intelligent Brush Recommendation":
                    ApplyBrushRecommendationSuggestion(suggestion);
                    break;

                case "Canvas Performance Optimization":
                    ApplyPerformanceOptimizationSuggestion(suggestion);
                    break;

                case "Color Theme Adaptation":
                    ApplyColorThemeAdaptationSuggestion(suggestion);
                    break;

                default:
                    _logger.LogWarning("Unknown AI suggestion type: {Type}", suggestion.SuggestionType);
                    break;
            }

            PreviewUpdateRequested?.Invoke();
            _logger.LogInformation("Successfully applied AI suggestion: {Type}", suggestion.SuggestionType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying AI suggestion: {Type}", suggestion?.SuggestionType);
        }
    }

    // Enhanced 3D and Lighting Color Pickers
    [RelayCommand]
    private void ChooseSecondaryLightColor()
    {
        var color = ShowColorPicker(SecondaryLightColor);
        if (color != null)
        {
            SecondaryLightColor = color;
            _logger.LogInformation("Secondary light color changed to {Color}", color);
        }
    }

    [RelayCommand]
    private void ChooseAmbientColor()
    {
        var color = ShowColorPicker(AmbientColor);
        if (color != null)
        {
            AmbientColor = color;
            _logger.LogInformation("Ambient color changed to {Color}", color);
        }
    }

    [RelayCommand]
    private void ChooseTertiaryLightColor()
    {
        var color = ShowColorPicker(TertiaryLightColor);
        if (color != null)
        {
            TertiaryLightColor = color;
            _logger.LogInformation("Tertiary light color changed to {Color}", color);
        }
    }

    [RelayCommand]
    private void ChooseQuaternaryLightColor()
    {
        var color = ShowColorPicker(QuaternaryLightColor);
        if (color != null)
        {
            QuaternaryLightColor = color;
            _logger.LogInformation("Quaternary light color changed to {Color}", color);
        }
    }

    [RelayCommand]
    private void ChooseEnvironmentMap()
    {
        var openFileDialog = new Microsoft.Win32.OpenFileDialog
        {
            Title = "Select Environment Map",
            Filter = "Image Files|*.png;*.jpg;*.jpeg;*.bmp;*.hdr;*.exr|All Files|*.*",
            Multiselect = false
        };

        if (openFileDialog.ShowDialog() == true)
        {
            EnvironmentMapPath = openFileDialog.FileName;
            _logger.LogInformation("Environment map selected: {Path}", EnvironmentMapPath);
        }
    }

    /// <summary>
    /// Applies a lighting preset configuration to all light sources
    /// </summary>
    [RelayCommand]
    private void ApplyLightingPreset()
    {
        try
        {
            ApplyLightingPresetConfiguration(LightingPreset);
            _logger.LogInformation("Applied lighting preset: {Preset}", LightingPreset);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying lighting preset: {Preset}", LightingPreset);
        }
    }

    // Drag and Drop Commands
    [RelayCommand]
    private void ToggleDragMode()
    {
        IsDragModeEnabled = !IsDragModeEnabled;
        _logger.LogInformation("Drag mode {Status}", IsDragModeEnabled ? "enabled" : "disabled");

        if (IsDragModeEnabled)
        {
            RegisterDraggableElements();
        }
        else
        {
            _dragDropService.ClearElements();
        }
    }

    [RelayCommand]
    private void ToggleAdvancedMode()
    {
        AdvancedModeEnabled = !AdvancedModeEnabled;
        LayerPanelVisible = AdvancedModeEnabled;
        _layerManager.AdvancedModeEnabled = AdvancedModeEnabled;

        _logger.LogInformation("Advanced mode {Status}", AdvancedModeEnabled ? "enabled" : "disabled");
    }

    [RelayCommand]
    private void ToggleLayerPanel()
    {
        LayerPanelVisible = !LayerPanelVisible;
        _logger.LogInformation("Layer panel {Status}", LayerPanelVisible ? "visible" : "hidden");
    }

    [RelayCommand]
    private void SelectLayer(ClockLayer layer)
    {
        if (layer == null) return;

        SelectedLayer = layer;

        // Update layer properties for UI binding
        LayerLocked = layer.Element.IsLocked;
        LayerRotation = layer.Element.Rotation;
        LayerScaleX = layer.Element.ScaleX;
        LayerScaleY = layer.Element.ScaleY;
        LayerSkewX = layer.Element.SkewX;
        LayerSkewY = layer.Element.SkewY;
        LayerBlendMode = layer.BlendMode;

        _logger.LogDebug("Selected layer: {LayerName}", layer.Name);
    }

    [RelayCommand]
    private void ToggleLayerVisibility(ClockLayer layer)
    {
        if (layer == null) return;

        _layerManager.SetLayerVisibility(layer, !layer.IsVisible);
        _logger.LogDebug("Toggled visibility for layer: {LayerName} to {IsVisible}", layer.Name, layer.IsVisible);
    }

    [RelayCommand]
    private void ToggleLayerLock(ClockLayer layer)
    {
        if (layer == null) return;

        var newLockState = !layer.Element.IsLocked;
        _layerManager.SetLayerLocked(layer, newLockState);

        if (SelectedLayer?.Id == layer.Id)
        {
            LayerLocked = newLockState;
        }

        _logger.LogDebug("Toggled lock for layer: {LayerName} to {IsLocked}", layer.Name, newLockState);
    }

    [RelayCommand]
    private void MoveLayerUp(ClockLayer layer)
    {
        if (layer == null) return;

        _layerManager.MoveLayerUp(layer);
        _logger.LogDebug("Moved layer up: {LayerName}", layer.Name);
    }

    [RelayCommand]
    private void MoveLayerDown(ClockLayer layer)
    {
        if (layer == null) return;

        _layerManager.MoveLayerDown(layer);
        _logger.LogDebug("Moved layer down: {LayerName}", layer.Name);
    }

    [RelayCommand]
    private void MoveLayerToTop(ClockLayer layer)
    {
        if (layer == null) return;

        _layerManager.MoveLayerToTop(layer);
        _logger.LogDebug("Moved layer to top: {LayerName}", layer.Name);
    }

    [RelayCommand]
    private void MoveLayerToBottom(ClockLayer layer)
    {
        if (layer == null) return;

        _layerManager.MoveLayerToBottom(layer);
        _logger.LogDebug("Moved layer to bottom: {LayerName}", layer.Name);
    }

    // Layer Property Change Handlers
    partial void OnAdvancedModeEnabledChanged(bool value)
    {
        LayerPanelVisible = value;
        _layerManager.AdvancedModeEnabled = value;
        PreviewUpdateRequested?.Invoke();
    }

    partial void OnLayerRotationChanged(double value)
    {
        if (SelectedLayer != null)
        {
            _layerManager.SetLayerTransformation(SelectedLayer, value, SelectedLayer.Element.ScaleX, SelectedLayer.Element.ScaleY, SelectedLayer.Element.SkewX, SelectedLayer.Element.SkewY);
        }
    }

    partial void OnLayerScaleXChanged(double value)
    {
        if (SelectedLayer != null)
        {
            _layerManager.SetLayerTransformation(SelectedLayer, SelectedLayer.Element.Rotation, value, SelectedLayer.Element.ScaleY, SelectedLayer.Element.SkewX, SelectedLayer.Element.SkewY);
        }
    }

    partial void OnLayerScaleYChanged(double value)
    {
        if (SelectedLayer != null)
        {
            _layerManager.SetLayerTransformation(SelectedLayer, SelectedLayer.Element.Rotation, SelectedLayer.Element.ScaleX, value, SelectedLayer.Element.SkewX, SelectedLayer.Element.SkewY);
        }
    }

    partial void OnLayerSkewXChanged(double value)
    {
        if (SelectedLayer != null)
        {
            _layerManager.SetLayerTransformation(SelectedLayer, SelectedLayer.Element.Rotation, SelectedLayer.Element.ScaleX, SelectedLayer.Element.ScaleY, value, SelectedLayer.Element.SkewY);
        }
    }

    partial void OnLayerSkewYChanged(double value)
    {
        if (SelectedLayer != null)
        {
            _layerManager.SetLayerTransformation(SelectedLayer, SelectedLayer.Element.Rotation, SelectedLayer.Element.ScaleX, SelectedLayer.Element.ScaleY, SelectedLayer.Element.SkewX, value);
        }
    }

    partial void OnLayerBlendModeChanged(ClockBlendMode value)
    {
        if (SelectedLayer != null)
        {
            _layerManager.SetLayerBlendMode(SelectedLayer, value);
        }
    }

    // Note: Undo/Redo functionality will be implemented when TransformationHistory is available

    // Mouse Event Handlers for Preview Canvas
    /// <summary>
    /// Handles mouse down events on the preview canvas for drag operations
    /// Last Updated: 2025-01-11 18:30:00 UTC
    /// </summary>
    /// <param name="position">Mouse position</param>
    [TestableMethod("PreviewInteraction", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 50)]
    public void OnPreviewMouseDown(System.Windows.Point position)
    {
        if (!IsDragModeEnabled) return;

        var hitElement = _dragDropService.HitTest(position);
        if (hitElement != null)
        {
            _dragDropService.StartDrag(hitElement, position);
            _logger.LogDebug("Started dragging element: {Id}", hitElement.Id);
        }
    }

    /// <summary>
    /// Handles mouse move events on the preview canvas for drag operations
    /// Last Updated: 2025-01-11 18:30:00 UTC
    /// </summary>
    /// <param name="position">Mouse position</param>
    [TestableMethod("PreviewInteraction", IncludeParameterValidation = true, IncludePerformanceTests = true, ExpectedExecutionTimeMs = 25)]
    public void OnPreviewMouseMove(System.Windows.Point position)
    {
        if (!IsDragModeEnabled) return;

        var dragState = _dragDropService.GetDragState();
        if (dragState.IsDragging)
        {
            _dragDropService.UpdateDrag(position);

            // Update the corresponding ViewModel properties
            UpdateViewModelFromDraggedElement(dragState.DraggedElement);

            // Trigger preview update
            PreviewUpdateRequested?.Invoke();
        }
    }

    /// <summary>
    /// Handles mouse up events on the preview canvas for drag operations
    /// Last Updated: 2025-01-11 18:30:00 UTC
    /// </summary>
    /// <param name="position">Mouse position</param>
    [TestableMethod("PreviewInteraction", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 50)]
    public void OnPreviewMouseUp(System.Windows.Point position)
    {
        if (!IsDragModeEnabled) return;

        var dragState = _dragDropService.GetDragState();
        if (dragState.IsDragging)
        {
            _dragDropService.EndDrag();
            _logger.LogDebug("Ended drag operation");
        }
    }

    /// <summary>
    /// Gets the current drag state for UI feedback
    /// Last Updated: 2025-01-11 18:30:00 UTC
    /// </summary>
    /// <returns>Current drag state</returns>
    [TestableMethod("DragDropState", IncludeExceptionTests = true, ExpectedExecutionTimeMs = 10)]
    public DragState? GetDragState()
    {
        return _dragDropService?.GetDragState();
    }

    /// <summary>
    /// Performs hit testing for draggable elements at the given position
    /// Last Updated: 2025-01-11 18:30:00 UTC
    /// </summary>
    /// <param name="position">Position to test</param>
    /// <returns>Hit draggable element or null</returns>
    [TestableMethod("DragDropHitTest", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 25)]
    public DraggablePreviewElement? HitTestDraggableElement(System.Windows.Point position)
    {
        if (!IsDragModeEnabled) return null;
        return _dragDropService?.HitTest(position);
    }

    /// <summary>
    /// Initializes the drag-drop service with event handlers
    /// </summary>
    private void InitializeDragDropService()
    {
        try
        {
            // Configure drag-drop service
            _dragDropService.GridSize = GridSize;
            _dragDropService.SnapToGridEnabled = SnapToGridEnabled;
            _dragDropService.ShowGrid = ShowGrid;

            // Subscribe to events
            _dragDropService.ElementPositionChanged += OnElementPositionChanged;
            _dragDropService.DragStateChanged += OnDragStateChanged;
            _dragDropService.ElementsUpdated += OnElementsUpdated;

            // Subscribe to property changes to keep drag service in sync
            PropertyChanged += OnViewModelPropertyChanged;

            _logger.LogDebug("Drag-drop service initialized");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error initializing drag-drop service");
        }
    }

    /// <summary>
    /// Handles ViewModel property changes to sync with drag-drop service
    /// </summary>
    private void OnViewModelPropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        try
        {
            switch (e.PropertyName)
            {
                case nameof(GridSize):
                    _dragDropService.GridSize = GridSize;
                    break;
                case nameof(SnapToGridEnabled):
                    _dragDropService.SnapToGridEnabled = SnapToGridEnabled;
                    break;
                case nameof(ShowGrid):
                    _dragDropService.ShowGrid = ShowGrid;
                    // Trigger preview update when grid visibility changes
                    PreviewUpdateRequested?.Invoke();
                    break;
                case nameof(IsDragModeEnabled):
                    // Trigger preview update when drag mode changes
                    PreviewUpdateRequested?.Invoke();
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling property change: {PropertyName}", e.PropertyName);
        }
    }

    /// <summary>
    /// Registers draggable elements with the drag-drop service
    /// </summary>
    private void RegisterDraggableElements()
    {
        try
        {
            _dragDropService.ClearElements();

            // Register time element
            _dragDropService.RegisterElement(new DraggablePreviewElement
            {
                Id = "time",
                DisplayName = "Time",
                Type = ElementType.Time,
                Element = new PositionableElement { OffsetX = 0, OffsetY = 0, IsDraggable = true }
            });

            // Register date components if visible
            if (ShowDate)
            {
                if (ShowDay)
                {
                    _dragDropService.RegisterElement(new DraggablePreviewElement
                    {
                        Id = "day",
                        DisplayName = "Day",
                        Type = ElementType.Day,
                        Element = new PositionableElement { OffsetX = DayOffsetX, OffsetY = DayOffsetY, IsDraggable = true }
                    });
                }

                if (ShowMonth)
                {
                    _dragDropService.RegisterElement(new DraggablePreviewElement
                    {
                        Id = "month",
                        DisplayName = "Month",
                        Type = ElementType.Month,
                        Element = new PositionableElement { OffsetX = MonthOffsetX, OffsetY = MonthOffsetY, IsDraggable = true }
                    });
                }

                if (ShowYear)
                {
                    _dragDropService.RegisterElement(new DraggablePreviewElement
                    {
                        Id = "year",
                        DisplayName = "Year",
                        Type = ElementType.Year,
                        Element = new PositionableElement { OffsetX = YearOffsetX, OffsetY = YearOffsetY, IsDraggable = true }
                    });
                }
            }

            // Register AM/PM element if visible
            if (ShowAmPm && !Use24Hour)
            {
                _dragDropService.RegisterElement(new DraggablePreviewElement
                {
                    Id = "ampm",
                    DisplayName = "AM/PM",
                    Type = ElementType.AmPm,
                    Element = new PositionableElement { OffsetX = AmPmOffsetX, OffsetY = AmPmOffsetY, IsDraggable = true }
                });
            }

            _logger.LogDebug("Registered {Count} draggable elements", _dragDropService.GetElements().Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error registering draggable elements");
        }
    }

    /// <summary>
    /// Updates ViewModel properties from dragged element position
    /// </summary>
    private void UpdateViewModelFromDraggedElement(DraggablePreviewElement? element)
    {
        if (element == null) return;

        try
        {
            switch (element.Type)
            {
                case ElementType.Day:
                    DayOffsetX = element.Element.OffsetX;
                    DayOffsetY = element.Element.OffsetY;
                    break;
                case ElementType.Month:
                    MonthOffsetX = element.Element.OffsetX;
                    MonthOffsetY = element.Element.OffsetY;
                    break;
                case ElementType.Year:
                    YearOffsetX = element.Element.OffsetX;
                    YearOffsetY = element.Element.OffsetY;
                    break;
                case ElementType.AmPm:
                    AmPmOffsetX = element.Element.OffsetX;
                    AmPmOffsetY = element.Element.OffsetY;
                    break;
                case ElementType.Time:
                    // Time position could be handled here if needed
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating ViewModel from dragged element: {Id}", element.Id);
        }
    }

    /// <summary>
    /// Event handler for element position changes
    /// </summary>
    private void OnElementPositionChanged(DraggablePreviewElement element)
    {
        _logger.LogDebug("Element {Id} position changed to ({X:F1}, {Y:F1})",
            element.Id, element.Element.OffsetX, element.Element.OffsetY);
    }



    /// <summary>
    /// Event handler for drag state changes
    /// </summary>
    private void OnDragStateChanged(DragState dragState)
    {
        // Could be used for visual feedback in the UI
        _logger.LogDebug("Drag state changed: IsDragging={IsDragging}, Element={Element}",
            dragState.IsDragging, dragState.DraggedElement?.Id);
    }

    /// <summary>
    /// Event handler for elements updated
    /// </summary>
    private void OnElementsUpdated()
    {
        _logger.LogDebug("Draggable elements updated");
    }

    /// <summary>
    /// Initializes layer manager event handlers
    /// </summary>
    private void InitializeLayerManagerEvents()
    {
        try
        {
            _layerManager.LayerAdded += OnLayerAdded;
            _layerManager.LayerRemoved += OnLayerRemoved;
            _layerManager.LayerChanged += OnLayerChanged;
            _layerManager.LayersReordered += OnLayersReordered;

            _logger.LogDebug("Layer manager events initialized");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error initializing layer manager events");
        }
    }

    /// <summary>
    /// Event handler for layer added
    /// </summary>
    private void OnLayerAdded(ClockLayer layer)
    {
        _logger.LogDebug("Layer added: {LayerName}", layer.Name);
        OnPropertyChanged(nameof(ClockLayers));
        PreviewUpdateRequested?.Invoke();
    }

    /// <summary>
    /// Event handler for layer removed
    /// </summary>
    private void OnLayerRemoved(ClockLayer layer)
    {
        _logger.LogDebug("Layer removed: {LayerName}", layer.Name);
        OnPropertyChanged(nameof(ClockLayers));
        PreviewUpdateRequested?.Invoke();
    }

    /// <summary>
    /// Event handler for layer changed
    /// </summary>
    private void OnLayerChanged(ClockLayer layer)
    {
        _logger.LogDebug("Layer changed: {LayerName}", layer.Name);
        PreviewUpdateRequested?.Invoke();
    }

    /// <summary>
    /// Event handler for layers reordered
    /// </summary>
    private void OnLayersReordered()
    {
        _logger.LogDebug("Layers reordered");
        OnPropertyChanged(nameof(ClockLayers));
        PreviewUpdateRequested?.Invoke();
    }

    /// <summary>
    /// Updates hit-test bounds for a date component to enable drag-drop
    /// </summary>
    private void UpdateComponentHitTestBounds(DateComponent component, float x, float y, SKPaint basePaint, ClockSettings settings)
    {
        try
        {
            var measurePaint = _paintPool.Get();
            try
            {
                // Copy properties from base paint
                measurePaint.TextSize = basePaint.TextSize;
                measurePaint.Typeface = basePaint.Typeface;
                measurePaint.IsAntialias = basePaint.IsAntialias;

                ApplyComponentStyling(measurePaint, component, settings);

                var textBounds = new SKRect();
                measurePaint.MeasureText(component.Value, ref textBounds);

                // Find corresponding draggable element and update its bounds
                var elementId = GetElementIdFromComponent(component);
                var draggableElement = _dragDropService.GetElements().FirstOrDefault(e => e.Id == elementId);

                if (draggableElement != null)
                {
                    draggableElement.Element.UpdateHitTestBounds(new System.Windows.Rect(
                        x, y - textBounds.Height, textBounds.Width, textBounds.Height));
                }
            }
            finally
            {
                _paintPool.Return(measurePaint);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating hit-test bounds for component");
        }
    }

    /// <summary>
    /// Gets the element ID corresponding to a date component
    /// </summary>
    private string GetElementIdFromComponent(DateComponent component)
    {
        return component.Type switch
        {
            DateComponentType.Day => "day",
            DateComponentType.Month => "month",
            DateComponentType.Year => "year",
            DateComponentType.AmPm => "ampm",
            DateComponentType.Time => "time",
            _ => "unknown"
        };
    }

    [RelayCommand]
    private void ChooseBackgroundImage()
    {
        var openFileDialog = new Microsoft.Win32.OpenFileDialog
        {
            Title = "Select Background Image",
            Filter = "Image Files|*.png;*.jpg;*.jpeg;*.bmp;*.gif|All Files|*.*",
            Multiselect = false
        };

        if (openFileDialog.ShowDialog() == true)
        {
            BackgroundImagePath = openFileDialog.FileName;
            _logger.LogInformation("Background image selected: {Path}", BackgroundImagePath);
        }
    }

    /// <summary>
    /// Renders the clock with specified parameters on the provided canvas
    /// Last Updated: 2025-01-11 18:30:00 UTC
    /// </summary>
    /// <param name="canvas">Canvas to render on</param>
    /// <param name="width">Canvas width</param>
    /// <param name="height">Canvas height</param>
    [TestableMethod("ClockRendering", IncludePerformanceTests = true, IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 100)]
    public void RenderClock(SKCanvas canvas, int width, int height)
    {
        try
        {
            // Clear canvas
            canvas.Clear(SKColors.Transparent);

            // Use layer-based rendering if advanced mode is enabled
            if (AdvancedModeEnabled)
            {
                RenderWithLayers(canvas, width, height);
            }
            else
            {
                RenderTraditional(canvas, width, height);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rendering clock");
        }
    }

    /// <summary>
    /// Renders the clock using the traditional method (non-layered)
    /// </summary>
    private void RenderTraditional(SKCanvas canvas, int width, int height)
    {
        try
        {
            // Render background shape if not transparent and no background image
            if (!TransparentBackground && string.IsNullOrEmpty(BackgroundImagePath))
            {
                RenderBackgroundShape(canvas, width, height);
            }

            // Render background image if set (replaces background color)
            if (!string.IsNullOrEmpty(BackgroundImagePath) && !TransparentBackground)
            {
                RenderBackgroundImage(canvas, width, height);
            }

            // Get current time
            var currentTime = DateTime.Now;
            string timeString;

            if (Use24Hour)
            {
                timeString = currentTime.ToString("HH:mm");
            }
            else
            {
                timeString = ShowAmPm ? currentTime.ToString("hh:mm tt") : currentTime.ToString("hh:mm");
            }

            // Get paint from pool for text rendering
            var paint = _paintPool.Get();
            try
            {
                paint.Color = ToSKColor(TextColor);
                paint.TextSize = (float)(FontSize * PreviewScale / 100);
                paint.IsAntialias = true;
                paint.Typeface = GetTypeface(SelectedFont);

                // Measure and center text first
                var textBounds = new SKRect();
                paint.MeasureText(timeString, ref textBounds);

                var x = (width - textBounds.Width) / 2;
                var y = (height - textBounds.Height) / 2 + textBounds.Height;

                // Draw glow effect if enabled (proper glow, not just blur)
                if (EnableGlow)
                {
                    DrawGlowEffectWithOpacity(canvas, timeString, x, y, paint);
                }

                // Draw shadow if enabled
                if (EnableShadow)
                {
                    DrawShadowWithOpacity(canvas, timeString, x, y, paint);
                }

                // Draw stroke if enabled
                if (EnableStroke)
                {
                    DrawStrokeWithOpacity(canvas, timeString, x, y, paint);
                }

                // Draw main text (3D or regular) with text opacity
                if (Enable3D)
                {
                    Draw3DTextWithOpacity(canvas, timeString, x, y, paint);
                }
                else
                {
                    DrawTextWithOpacity(canvas, timeString, x, y, paint);
                }

                // Draw date components if enabled
                if (ShowDate)
                {
                    RenderDateComponents(canvas, currentTime, paint, width, height, y);
                }

                // Draw drag-and-drop visual feedback if enabled
                if (IsDragModeEnabled)
                {
                    RenderDragVisualFeedback(canvas, width, height);
                }
            }
            finally
            {
                // Return paint to pool for reuse
                _paintPool.Return(paint);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rendering traditional clock");
        }
    }

    /// <summary>
    /// Renders the clock using the advanced layer system
    /// </summary>
    private void RenderWithLayers(SKCanvas canvas, int width, int height)
    {
        try
        {
            // TEMPORARY FIX: Layer system is not fully implemented yet
            // Fall back to traditional rendering until layer content rendering is complete
            _logger.LogDebug("Layer system not fully implemented, using traditional rendering in advanced mode");
            RenderTraditional(canvas, width, height);
            return;

            // TODO: Uncomment when layer system is fully implemented
            /*
            // Create render context with current settings and time
            var renderContext = new ClockRenderContext
            {
                CurrentTime = DateTime.Now,
                Settings = CreateClockSettings(),
                Width = width,
                Height = height,
                ViewModel = this
            };

            // Use the layer manager to render all layers
            _layerManager.RenderLayers(canvas, width, height, renderContext);
            */
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rendering layered clock, falling back to traditional rendering");

            // Fallback to traditional rendering
            RenderTraditional(canvas, width, height);
        }
    }

    /// <summary>
    /// Renders visual feedback for drag-and-drop operations
    /// </summary>
    /// <param name="canvas">Canvas to render to</param>
    /// <param name="width">Canvas width</param>
    /// <param name="height">Canvas height</param>
    private void RenderDragVisualFeedback(SKCanvas canvas, int width, int height)
    {
        try
        {
            // Draw grid if enabled
            if (ShowGrid)
            {
                RenderSnapGrid(canvas, width, height);
            }

            // Draw drag indicators for currently dragged element
            var dragState = _dragDropService.GetDragState();
            if (dragState.IsDragging && dragState.DraggedElement != null)
            {
                RenderDragIndicator(canvas, dragState.DraggedElement, dragState.CurrentPosition);
            }

            // Draw hover indicators for draggable elements
            RenderDraggableElementIndicators(canvas, width, height);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rendering drag visual feedback");
        }
    }

    /// <summary>
    /// Renders the snap-to-grid overlay
    /// </summary>
    /// <param name="canvas">Canvas to render to</param>
    /// <param name="width">Canvas width</param>
    /// <param name="height">Canvas height</param>
    private void RenderSnapGrid(SKCanvas canvas, int width, int height)
    {
        if (GridSize <= 0) return;

        var gridPaint = _paintPool.Get();
        try
        {
            gridPaint.Color = SKColors.Gray.WithAlpha(50); // Semi-transparent grid
            gridPaint.StrokeWidth = 1;
            gridPaint.Style = SKPaintStyle.Stroke;
            gridPaint.IsAntialias = true;

            // Draw vertical grid lines
            for (float x = 0; x <= width; x += (float)GridSize)
            {
                canvas.DrawLine(x, 0, x, height, gridPaint);
            }

            // Draw horizontal grid lines
            for (float y = 0; y <= height; y += (float)GridSize)
            {
                canvas.DrawLine(0, y, width, y, gridPaint);
            }
        }
        finally
        {
            _paintPool.Return(gridPaint);
        }
    }

    /// <summary>
    /// Renders drag indicator for the currently dragged element
    /// </summary>
    /// <param name="canvas">Canvas to render to</param>
    /// <param name="draggedElement">Element being dragged</param>
    /// <param name="currentPosition">Current drag position</param>
    private void RenderDragIndicator(SKCanvas canvas, DraggablePreviewElement draggedElement, System.Windows.Point currentPosition)
    {
        var highlightPaint = _paintPool.Get();
        var borderPaint = _paintPool.Get();
        var textPaint = _paintPool.Get();
        try
        {
            // Configure highlight paint
            highlightPaint.Color = SKColors.CornflowerBlue.WithAlpha(100);
            highlightPaint.Style = SKPaintStyle.Fill;
            highlightPaint.IsAntialias = true;

            // Configure border paint
            borderPaint.Color = SKColors.CornflowerBlue;
            borderPaint.Style = SKPaintStyle.Stroke;
            borderPaint.StrokeWidth = 2;
            borderPaint.IsAntialias = true;

            // Create a highlight rectangle around the element
            var highlightRect = new SKRect(
                (float)(currentPosition.X - 20),
                (float)(currentPosition.Y - 10),
                (float)(currentPosition.X + 20),
                (float)(currentPosition.Y + 10)
            );

            canvas.DrawRoundRect(highlightRect, 5, 5, highlightPaint);
            canvas.DrawRoundRect(highlightRect, 5, 5, borderPaint);

            // Configure text paint
            textPaint.Color = SKColors.White;
            textPaint.TextSize = 12;
            textPaint.IsAntialias = true;
            textPaint.Typeface = SKTypeface.FromFamilyName("Arial", SKFontStyle.Bold);

            var textBounds = new SKRect();
            textPaint.MeasureText(draggedElement.DisplayName, ref textBounds);
            var textX = (float)(currentPosition.X - textBounds.Width / 2);
            var textY = (float)(currentPosition.Y + textBounds.Height / 2);

            canvas.DrawText(draggedElement.DisplayName, textX, textY, textPaint);
        }
        finally
        {
            _paintPool.Return(highlightPaint);
            _paintPool.Return(borderPaint);
            _paintPool.Return(textPaint);
        }
    }

    /// <summary>
    /// Renders indicators for all draggable elements
    /// </summary>
    /// <param name="canvas">Canvas to render to</param>
    /// <param name="width">Canvas width</param>
    /// <param name="height">Canvas height</param>
    private void RenderDraggableElementIndicators(SKCanvas canvas, int width, int height)
    {
        var elements = _dragDropService.GetElements();

        var indicatorPaint = _paintPool.Get();
        try
        {
            indicatorPaint.Color = SKColors.Orange.WithAlpha(80);
            indicatorPaint.Style = SKPaintStyle.Stroke;
            indicatorPaint.StrokeWidth = 1;
            indicatorPaint.PathEffect = SKPathEffect.CreateDash(new float[] { 5, 5 }, 0);
            indicatorPaint.IsAntialias = true;

            foreach (var element in elements)
            {
                if (element.IsVisible && element.Element.IsDraggable)
                {
                    // Draw a dashed border around draggable elements
                    var elementRect = new SKRect(
                        (float)(element.Element.OffsetX - 15),
                        (float)(element.Element.OffsetY - 8),
                        (float)(element.Element.OffsetX + 15),
                        (float)(element.Element.OffsetY + 8)
                    );

                    canvas.DrawRect(elementRect, indicatorPaint);
                }
            }
        }
        finally
        {
            _paintPool.Return(indicatorPaint);
        }
    }

    /// <summary>
    /// Renders date components using the DateComponentManager
    /// </summary>
    private void RenderDateComponents(SKCanvas canvas, DateTime currentTime, SKPaint basePaint, int width, int height, float timeY)
    {
        try
        {
            // Determine which date format to use
            var formatToUse = UseCustomDateFormat ? CustomDateFormat : DateFormat;

            // Parse the date format into components
            var components = _dateComponentManager.ParseDateFormat(formatToUse);

            if (components.Count == 0)
            {
                _logger.LogWarning("No date components found for format: {Format}", formatToUse);
                return;
            }

            // Create settings object for component rendering
            var settings = CreateClockSettings();

            // Calculate base position for date components
            var baseY = timeY + 40; // Position below the time
            var totalWidth = CalculateComponentsWidth(components, currentTime, basePaint);
            var baseX = (width - totalWidth) / 2; // Center horizontally

            // Render each component
            var currentX = baseX;
            foreach (var component in components)
            {
                // Update component value for width calculation
                component.Value = GetComponentValue(component, currentTime);

                if (!string.IsNullOrEmpty(component.Value))
                {
                    // Render the component
                    _dateComponentManager.RenderDateComponent(
                        canvas, component, currentTime, basePaint, settings, currentX, baseY);

                    // Update hit-test bounds for drag-drop if enabled
                    if (IsDragModeEnabled)
                    {
                        UpdateComponentHitTestBounds(component, currentX, baseY, basePaint, settings);
                    }

                    // Move to next position
                    var measurePaint = _paintPool.Get();
                    try
                    {
                        // Copy properties from base paint
                        measurePaint.TextSize = basePaint.TextSize;
                        measurePaint.Typeface = basePaint.Typeface;
                        measurePaint.IsAntialias = basePaint.IsAntialias;

                        ApplyComponentStyling(measurePaint, component, settings);
                        var textBounds = new SKRect();
                        measurePaint.MeasureText(component.Value, ref textBounds);
                        currentX += textBounds.Width;
                    }
                    finally
                    {
                        _paintPool.Return(measurePaint);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rendering date components");
        }
    }

    /// <summary>
    /// Calculates the total width of all date components
    /// </summary>
    private float CalculateComponentsWidth(List<DateComponent> components, DateTime dateTime, SKPaint basePaint)
    {
        float totalWidth = 0;
        var settings = CreateClockSettings();

        foreach (var component in components)
        {
            component.Value = GetComponentValue(component, dateTime);
            if (!string.IsNullOrEmpty(component.Value))
            {
                var measurePaint = _paintPool.Get();
                try
                {
                    // Copy properties from base paint
                    measurePaint.TextSize = basePaint.TextSize;
                    measurePaint.Typeface = basePaint.Typeface;
                    measurePaint.IsAntialias = basePaint.IsAntialias;

                    ApplyComponentStyling(measurePaint, component, settings);
                    var textBounds = new SKRect();
                    measurePaint.MeasureText(component.Value, ref textBounds);
                    totalWidth += textBounds.Width;
                }
                finally
                {
                    _paintPool.Return(measurePaint);
                }
            }
        }

        return totalWidth;
    }

    /// <summary>
    /// Gets the formatted value for a date component
    /// </summary>
    private string GetComponentValue(DateComponent component, DateTime dateTime)
    {
        try
        {
            // For separator components, return the format string directly (no DateTime formatting needed)
            if (component.Type == DateComponentType.Separator)
            {
                return component.Format;
            }

            // For date/time components, format using DateTime
            return dateTime.ToString(component.Format, System.Globalization.CultureInfo.CurrentCulture);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error formatting component with format '{Format}'", component.Format);
            return component.Format; // Fallback to the format string itself
        }
    }

    /// <summary>
    /// Applies component-specific styling to paint
    /// </summary>
    private void ApplyComponentStyling(SKPaint paint, DateComponent component, ClockSettings settings)
    {
        switch (component.Type)
        {
            case DateComponentType.Day:
                paint.Color = ToSKColor(settings.DateColor);
                paint.TextSize = (float)settings.DayFontSize;
                break;
            case DateComponentType.Month:
                paint.Color = ToSKColor(settings.DateColor);
                paint.TextSize = (float)settings.MonthFontSize;
                break;
            case DateComponentType.Year:
                paint.Color = ToSKColor(settings.DateColor);
                paint.TextSize = (float)settings.YearFontSize;
                break;
            case DateComponentType.AmPm:
                paint.Color = ToSKColor(settings.AmPmColor);
                paint.TextSize = (float)settings.AmPmFontSize;
                break;
            case DateComponentType.Separator:
                paint.Color = ToSKColor(settings.DateColor);
                paint.TextSize = (float)settings.DateFontSize;
                break;
        }
    }

    private void RenderBackgroundShape(SKCanvas canvas, int width, int height)
    {
        try
        {
            // Calculate text bounds for background sizing
            var textBounds = CalculateTextBounds(width, height);

            // Calculate background dimensions
            float backgroundWidth, backgroundHeight;
            if (BackgroundWidth > 0 && BackgroundHeight > 0)
            {
                // Use custom dimensions if specified
                backgroundWidth = (float)BackgroundWidth;
                backgroundHeight = (float)BackgroundHeight;
            }
            else
            {
                // Default: 25% larger than text bounds
                var textWidth = textBounds.Width;
                var textHeight = textBounds.Height;
                backgroundWidth = textWidth * 1.25f;
                backgroundHeight = textHeight * 1.25f;

                // Ensure minimum size
                backgroundWidth = Math.Max(backgroundWidth, 50);
                backgroundHeight = Math.Max(backgroundHeight, 30);
            }

            // Calculate background position with offsets
            var centerX = width / 2f + (float)BackgroundOffsetX;
            var centerY = height / 2f + (float)BackgroundOffsetY;

            // Create background rectangle centered with offsets
            var left = centerX - backgroundWidth / 2f;
            var top = centerY - backgroundHeight / 2f;
            var right = left + backgroundWidth;
            var bottom = top + backgroundHeight;

            var rect = new SKRect(left, top, right, bottom);

            var paint = _paintPool.Get();
            try
            {
                paint.Color = ToSKColor(BackgroundColor);
                paint.IsAntialias = true;
                paint.Style = SKPaintStyle.Fill;

                // Apply feather/blur effect if enabled
                if (BackgroundFeather > 0)
                {
                    paint.MaskFilter = SKMaskFilter.CreateBlur(SKBlurStyle.Normal, (float)BackgroundFeather);
                }

                switch (BackgroundShape.ToLower())
                {
                    case "circle":
                        var radius = Math.Min(backgroundWidth, backgroundHeight) / 2;
                        canvas.DrawCircle(centerX, centerY, radius, paint);
                        break;

                    case "ellipse":
                        canvas.DrawOval(rect, paint);
                        break;

                    case "roundedrectangle":
                        var cornerRadius = (float)BackgroundCornerRadius;
                        canvas.DrawRoundRect(rect, cornerRadius, cornerRadius, paint);
                        break;

                    case "rectangle":
                    default:
                        canvas.DrawRect(rect, paint);
                        break;
                }
            }
            finally
            {
                _paintPool.Return(paint);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rendering background shape");
        }
    }

    /// <summary>
    /// Calculates the text bounds for background sizing
    /// </summary>
    private SKRect CalculateTextBounds(int width, int height)
    {
        try
        {
            // Get current time string
            var currentTime = DateTime.Now;
            string timeString;

            if (Use24Hour)
            {
                timeString = currentTime.ToString("HH:mm");
            }
            else
            {
                timeString = ShowAmPm ? currentTime.ToString("hh:mm tt") : currentTime.ToString("hh:mm");
            }

            // Get paint from pool for text measurement
            var paint = _paintPool.Get();
            try
            {
                paint.TextSize = (float)(FontSize * PreviewScale / 100);
                paint.IsAntialias = true;
                paint.Typeface = GetTypeface(SelectedFont);

                // Measure text bounds
                var textBounds = new SKRect();
                paint.MeasureText(timeString, ref textBounds);

                return textBounds;
            }
            finally
            {
                _paintPool.Return(paint);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating text bounds");
            // Return default bounds if calculation fails
            return new SKRect(0, 0, 200, 50);
        }
    }

    /// <summary>
    /// Renders background image with seamless borderless integration
    /// </summary>
    private void RenderBackgroundImage(SKCanvas canvas, int width, int height)
    {
        try
        {
            if (string.IsNullOrEmpty(BackgroundImagePath) || !File.Exists(BackgroundImagePath))
                return;

            using var imageData = SKData.Create(BackgroundImagePath);
            using var image = SKImage.FromEncodedData(imageData);

            if (image == null)
                return;

            var paint = _paintPool.Get();
            try
            {
                paint.IsAntialias = true;
                paint.FilterQuality = SKFilterQuality.High;

                // Create clipping path to ensure seamless integration
                canvas.Save();

                // Determine rendering mode based on background shape
                if (BackgroundShape?.ToLower() == "circle")
                {
                    RenderBackgroundImageCircular(canvas, image, paint, width, height);
                }
                else if (BackgroundShape?.ToLower() == "ellipse")
                {
                    RenderBackgroundImageElliptical(canvas, image, paint, width, height);
                }
                else if (BackgroundShape?.ToLower() == "roundedrectangle")
                {
                    RenderBackgroundImageRoundedRect(canvas, image, paint, width, height);
                }
                else
                {
                    RenderBackgroundImageRectangular(canvas, image, paint, width, height);
                }

                canvas.Restore();
            }
            finally
            {
                _paintPool.Return(paint);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rendering background image");
        }
    }

    /// <summary>
    /// Renders background image in rectangular mode with seamless borders
    /// </summary>
    private void RenderBackgroundImageRectangular(SKCanvas canvas, SKImage image, SKPaint paint, int width, int height)
    {
        // Calculate text bounds for proper sizing
        var textBounds = CalculateTextBounds(width, height);

        // Calculate background dimensions (25% larger than text by default)
        float backgroundWidth, backgroundHeight;
        if (BackgroundWidth > 0 && BackgroundHeight > 0)
        {
            backgroundWidth = (float)BackgroundWidth;
            backgroundHeight = (float)BackgroundHeight;
        }
        else
        {
            backgroundWidth = textBounds.Width * 1.25f;
            backgroundHeight = textBounds.Height * 1.25f;
            backgroundWidth = Math.Max(backgroundWidth, 100);
            backgroundHeight = Math.Max(backgroundHeight, 60);
        }

        // Calculate position with offsets
        var centerX = width / 2f + (float)BackgroundOffsetX;
        var centerY = height / 2f + (float)BackgroundOffsetY;

        var left = centerX - backgroundWidth / 2f;
        var top = centerY - backgroundHeight / 2f;
        var right = left + backgroundWidth;
        var bottom = top + backgroundHeight;

        var destRect = new SKRect(left, top, right, bottom);

        // Apply corner radius clipping if specified
        if (BackgroundCornerRadius > 0)
        {
            var clipPath = new SKPath();
            clipPath.AddRoundRect(destRect, (float)BackgroundCornerRadius, (float)BackgroundCornerRadius);
            canvas.ClipPath(clipPath, SKClipOperation.Intersect, true);
        }
        else
        {
            canvas.ClipRect(destRect);
        }

        // Draw image to fill the entire clipped area seamlessly
        canvas.DrawImage(image, destRect, paint);
    }

    /// <summary>
    /// Renders background image in circular mode
    /// </summary>
    private void RenderBackgroundImageCircular(SKCanvas canvas, SKImage image, SKPaint paint, int width, int height)
    {
        var textBounds = CalculateTextBounds(width, height);
        var radius = Math.Max(textBounds.Width, textBounds.Height) * 0.75f;

        var centerX = width / 2f + (float)BackgroundOffsetX;
        var centerY = height / 2f + (float)BackgroundOffsetY;

        // Create circular clipping path
        var clipPath = new SKPath();
        clipPath.AddCircle(centerX, centerY, radius);
        canvas.ClipPath(clipPath, SKClipOperation.Intersect, true);

        // Draw image to fill the circle
        var destRect = new SKRect(centerX - radius, centerY - radius, centerX + radius, centerY + radius);
        canvas.DrawImage(image, destRect, paint);
    }

    /// <summary>
    /// Renders background image in elliptical mode
    /// </summary>
    private void RenderBackgroundImageElliptical(SKCanvas canvas, SKImage image, SKPaint paint, int width, int height)
    {
        var textBounds = CalculateTextBounds(width, height);
        var radiusX = textBounds.Width * 0.75f;
        var radiusY = textBounds.Height * 0.75f;

        var centerX = width / 2f + (float)BackgroundOffsetX;
        var centerY = height / 2f + (float)BackgroundOffsetY;

        // Create elliptical clipping path
        var clipPath = new SKPath();
        var rect = new SKRect(centerX - radiusX, centerY - radiusY, centerX + radiusX, centerY + radiusY);
        clipPath.AddOval(rect);
        canvas.ClipPath(clipPath, SKClipOperation.Intersect, true);

        // Draw image to fill the ellipse
        canvas.DrawImage(image, rect, paint);
    }

    /// <summary>
    /// Renders background image in rounded rectangle mode
    /// </summary>
    private void RenderBackgroundImageRoundedRect(SKCanvas canvas, SKImage image, SKPaint paint, int width, int height)
    {
        var textBounds = CalculateTextBounds(width, height);

        float backgroundWidth = textBounds.Width * 1.25f;
        float backgroundHeight = textBounds.Height * 1.25f;

        var centerX = width / 2f + (float)BackgroundOffsetX;
        var centerY = height / 2f + (float)BackgroundOffsetY;

        var left = centerX - backgroundWidth / 2f;
        var top = centerY - backgroundHeight / 2f;
        var right = left + backgroundWidth;
        var bottom = top + backgroundHeight;

        var destRect = new SKRect(left, top, right, bottom);

        // Create rounded rectangle clipping path
        var clipPath = new SKPath();
        clipPath.AddRoundRect(destRect, (float)BackgroundCornerRadius, (float)BackgroundCornerRadius);
        canvas.ClipPath(clipPath, SKClipOperation.Intersect, true);

        // Draw image to fill the rounded rectangle
        canvas.DrawImage(image, destRect, paint);
    }

    private ClockSettings CreateClockSettings()
    {
        return new ClockSettings
        {
            AlwaysOnTop = AlwaysOnTop,
            ShowDate = ShowDate,
            ShowAmPm = ShowAmPm,
            Use24Hour = Use24Hour,
            FontFamily = SelectedFont,
            FontSize = FontSize,
            TextColor = TextColor,
            DateColor = DateColor,
            TransparentBackground = TransparentBackground,
            BackgroundColor = BackgroundColor,
            BackgroundImagePath = BackgroundImagePath,
            BackgroundShape = BackgroundShape,
            BackgroundCornerRadius = BackgroundCornerRadius,
            BackgroundFeather = BackgroundFeather,
            BackgroundPadding = BackgroundPadding,
            BackgroundWidth = BackgroundWidth,
            BackgroundHeight = BackgroundHeight,
            BackgroundOffsetX = BackgroundOffsetX,
            BackgroundOffsetY = BackgroundOffsetY,
            EnableGlow = EnableGlow,
            GlowColor = GlowColor,
            GlowRadius = GlowRadius,
            EnableShadow = EnableShadow,
            ShadowColor = ShadowColor,
            ShadowOffsetX = ShadowOffsetX,
            ShadowOffsetY = ShadowOffsetY,
            ShadowBlur = ShadowBlur,
            EnableStroke = EnableStroke,
            StrokeColor = StrokeColor,
            StrokeWidth = StrokeWidth,
            Enable3D = Enable3D,
            TextDepth = TextDepth,
            BevelSize = BevelSize,
            Text3DColor = Text3DColor,
            EnableLighting = EnableLighting,
            LightColor = LightColor,
            LightIntensity = LightIntensity,
            LightAngleX = LightAngleX,
            LightAngleY = LightAngleY,

            // Enhanced 3D and Lighting Properties
            Material = Material,
            Metallic = Metallic,
            Roughness = Roughness,
            Reflectance = Reflectance,
            EnableMultipleLights = EnableMultipleLights,
            SecondaryLightColor = SecondaryLightColor,
            SecondaryLightIntensity = SecondaryLightIntensity,
            SecondaryLightAngleX = SecondaryLightAngleX,
            SecondaryLightAngleY = SecondaryLightAngleY,
            SecondaryLightType = SecondaryLightType,

            // Tertiary Light Source
            EnableTertiaryLight = EnableTertiaryLight,
            TertiaryLightColor = TertiaryLightColor,
            TertiaryLightIntensity = TertiaryLightIntensity,
            TertiaryLightAngleX = TertiaryLightAngleX,
            TertiaryLightAngleY = TertiaryLightAngleY,
            TertiaryLightType = TertiaryLightType,

            // Quaternary Light Source
            EnableQuaternaryLight = EnableQuaternaryLight,
            QuaternaryLightColor = QuaternaryLightColor,
            QuaternaryLightIntensity = QuaternaryLightIntensity,
            QuaternaryLightAngleX = QuaternaryLightAngleX,
            QuaternaryLightAngleY = QuaternaryLightAngleY,
            QuaternaryLightType = QuaternaryLightType,

            // Light Source Management
            LightingPreset = LightingPreset,
            BlendingMode = BlendingMode,
            LegacyLightBlendingMode = LegacyLightBlendingMode,
            EnableLightPriority = EnableLightPriority,
            LightFalloffDistance = LightFalloffDistance,

            // Light Priority Settings
            PrimaryLightPriority = PrimaryLightPriority,
            SecondaryLightPriority = SecondaryLightPriority,
            TertiaryLightPriority = TertiaryLightPriority,
            QuaternaryLightPriority = QuaternaryLightPriority,

            // Advanced Light Properties
            SpotLightConeAngle = SpotLightConeAngle,
            SpotLightPenumbraAngle = SpotLightPenumbraAngle,
            AreaLightWidth = AreaLightWidth,
            AreaLightHeight = AreaLightHeight,
            EnableAdvancedLightFalloff = EnableAdvancedLightFalloff,

            EnableAmbientOcclusion = EnableAmbientOcclusion,
            AmbientOcclusionIntensity = AmbientOcclusionIntensity,
            AmbientOcclusionRadius = AmbientOcclusionRadius,
            AmbientColor = AmbientColor,
            AmbientIntensity = AmbientIntensity,
            EnableEnvironmentReflection = EnableEnvironmentReflection,
            ReflectionIntensity = ReflectionIntensity,
            EnvironmentMapPath = EnvironmentMapPath,
            EnableSoftShadows = EnableSoftShadows,
            ShadowSoftness = ShadowSoftness,
            EnableGlobalIllumination = EnableGlobalIllumination,
            MaxLightBounces = MaxLightBounces,

            Width = ClockWidth,
            Height = ClockHeight,

            // Granular Date Component Controls
            ShowDay = ShowDay,
            ShowMonth = ShowMonth,
            ShowYear = ShowYear,

            // Individual Date Component Positioning
            DayOffsetX = DayOffsetX,
            DayOffsetY = DayOffsetY,
            MonthOffsetX = MonthOffsetX,
            MonthOffsetY = MonthOffsetY,
            YearOffsetX = YearOffsetX,
            YearOffsetY = YearOffsetY,

            // Individual Date Component Font Sizes
            DayFontSize = DayFontSize,
            MonthFontSize = MonthFontSize,
            YearFontSize = YearFontSize,

            // Advanced Date Properties
            DateFormat = DateFormat,
            DateFontSize = DateFontSize,
            DateFontFamily = DateFontFamily,
            DateOffsetX = DateOffsetX,
            DateOffsetY = DateOffsetY,

            // AM/PM Properties
            AmPmColor = AmPmColor,
            AmPmFontSize = AmPmFontSize,
            AmPmFontFamily = AmPmFontFamily,
            AmPmOffsetX = AmPmOffsetX,
            AmPmOffsetY = AmPmOffsetY,

            // Custom Date Format
            UseCustomDateFormat = UseCustomDateFormat,
            CustomDateFormat = CustomDateFormat,

            // Opacity Controls
            TextOpacity = TextOpacity,
            StrokeOpacity = StrokeOpacity
        };
    }

    private void ApplySettings(ClockSettings settings)
    {
        AlwaysOnTop = settings.AlwaysOnTop;
        ShowDate = settings.ShowDate;
        ShowAmPm = settings.ShowAmPm;
        Use24Hour = settings.Use24Hour;
        SelectedFont = settings.FontFamily;
        FontSize = settings.FontSize;
        TextColor = settings.TextColor;
        DateColor = settings.DateColor;
        TransparentBackground = settings.TransparentBackground;
        BackgroundColor = settings.BackgroundColor;
        BackgroundImagePath = settings.BackgroundImagePath;
        BackgroundShape = settings.BackgroundShape;
        BackgroundCornerRadius = settings.BackgroundCornerRadius;
        BackgroundFeather = settings.BackgroundFeather;
        BackgroundPadding = settings.BackgroundPadding;
        BackgroundWidth = settings.BackgroundWidth;
        BackgroundHeight = settings.BackgroundHeight;
        BackgroundOffsetX = settings.BackgroundOffsetX;
        BackgroundOffsetY = settings.BackgroundOffsetY;
        EnableGlow = settings.EnableGlow;
        GlowColor = settings.GlowColor;
        GlowRadius = settings.GlowRadius;
        EnableShadow = settings.EnableShadow;
        ShadowColor = settings.ShadowColor;
        ShadowOffsetX = settings.ShadowOffsetX;
        ShadowOffsetY = settings.ShadowOffsetY;
        ShadowBlur = settings.ShadowBlur;
        EnableStroke = settings.EnableStroke;
        StrokeColor = settings.StrokeColor;
        StrokeWidth = settings.StrokeWidth;
        Enable3D = settings.Enable3D;
        TextDepth = settings.TextDepth;
        BevelSize = settings.BevelSize;
        Text3DColor = settings.Text3DColor;
        EnableLighting = settings.EnableLighting;
        LightColor = settings.LightColor;
        LightIntensity = settings.LightIntensity;
        LightAngleX = settings.LightAngleX;
        LightAngleY = settings.LightAngleY;

        // Enhanced 3D and Lighting Properties
        Material = settings.Material;
        Metallic = settings.Metallic;
        Roughness = settings.Roughness;
        Reflectance = settings.Reflectance;
        EnableMultipleLights = settings.EnableMultipleLights;
        SecondaryLightColor = settings.SecondaryLightColor;
        SecondaryLightIntensity = settings.SecondaryLightIntensity;
        SecondaryLightAngleX = settings.SecondaryLightAngleX;
        SecondaryLightAngleY = settings.SecondaryLightAngleY;
        SecondaryLightType = settings.SecondaryLightType;

        // Tertiary Light Source
        EnableTertiaryLight = settings.EnableTertiaryLight;
        TertiaryLightColor = settings.TertiaryLightColor;
        TertiaryLightIntensity = settings.TertiaryLightIntensity;
        TertiaryLightAngleX = settings.TertiaryLightAngleX;
        TertiaryLightAngleY = settings.TertiaryLightAngleY;
        TertiaryLightType = settings.TertiaryLightType;

        // Quaternary Light Source
        EnableQuaternaryLight = settings.EnableQuaternaryLight;
        QuaternaryLightColor = settings.QuaternaryLightColor;
        QuaternaryLightIntensity = settings.QuaternaryLightIntensity;
        QuaternaryLightAngleX = settings.QuaternaryLightAngleX;
        QuaternaryLightAngleY = settings.QuaternaryLightAngleY;
        QuaternaryLightType = settings.QuaternaryLightType;

        // Light Source Management
        LightingPreset = settings.LightingPreset;
        BlendingMode = settings.BlendingMode;
        LegacyLightBlendingMode = settings.LegacyLightBlendingMode;
        EnableLightPriority = settings.EnableLightPriority;
        LightFalloffDistance = settings.LightFalloffDistance;

        // Light Priority Settings
        PrimaryLightPriority = settings.PrimaryLightPriority;
        SecondaryLightPriority = settings.SecondaryLightPriority;
        TertiaryLightPriority = settings.TertiaryLightPriority;
        QuaternaryLightPriority = settings.QuaternaryLightPriority;

        // Advanced Light Properties
        SpotLightConeAngle = settings.SpotLightConeAngle;
        SpotLightPenumbraAngle = settings.SpotLightPenumbraAngle;
        AreaLightWidth = settings.AreaLightWidth;
        AreaLightHeight = settings.AreaLightHeight;
        EnableAdvancedLightFalloff = settings.EnableAdvancedLightFalloff;

        EnableAmbientOcclusion = settings.EnableAmbientOcclusion;
        AmbientOcclusionIntensity = settings.AmbientOcclusionIntensity;
        AmbientOcclusionRadius = settings.AmbientOcclusionRadius;
        AmbientColor = settings.AmbientColor;
        AmbientIntensity = settings.AmbientIntensity;
        EnableEnvironmentReflection = settings.EnableEnvironmentReflection;
        ReflectionIntensity = settings.ReflectionIntensity;
        EnvironmentMapPath = settings.EnvironmentMapPath;
        EnableSoftShadows = settings.EnableSoftShadows;
        ShadowSoftness = settings.ShadowSoftness;
        EnableGlobalIllumination = settings.EnableGlobalIllumination;
        MaxLightBounces = settings.MaxLightBounces;

        // Granular Date Component Controls
        ShowDay = settings.ShowDay;
        ShowMonth = settings.ShowMonth;
        ShowYear = settings.ShowYear;

        // Individual Date Component Positioning
        DayOffsetX = settings.DayOffsetX;
        DayOffsetY = settings.DayOffsetY;
        MonthOffsetX = settings.MonthOffsetX;
        MonthOffsetY = settings.MonthOffsetY;
        YearOffsetX = settings.YearOffsetX;
        YearOffsetY = settings.YearOffsetY;

        // Individual Date Component Font Sizes
        DayFontSize = settings.DayFontSize;
        MonthFontSize = settings.MonthFontSize;
        YearFontSize = settings.YearFontSize;

        ClockWidth = settings.Width;
        ClockHeight = settings.Height;
    }

    private void LoadSettings()
    {
        // TODO: Load settings from file
    }

    private readonly Dictionary<string, SKTypeface> _loadedFonts = new();
    private readonly Dictionary<string, string> _fontFilePaths = new();
    private readonly SemaphoreSlim _fontLoadingSemaphore = new(1, 1);
    private bool _fontsLoaded = false;

    /// <summary>
    /// Loads available fonts with individual testing and validation
    /// </summary>
    private async Task LoadAvailableFontsAsync(IProgress<FontLoadingProgress>? progress = null)
    {
        await _fontLoadingSemaphore.WaitAsync();
        try
        {
            if (_fontsLoaded) return;

            _logger.LogInformation("Starting enhanced font loading with individual validation...");
            var totalSteps = 4;
            var currentStep = 0;

            // Step 1: Load and validate system fonts
            progress?.Report(new FontLoadingProgress(++currentStep, totalSteps, "Loading system fonts..."));
            await LoadSystemFontsAsync();

            // Step 2: Scan asset directories
            progress?.Report(new FontLoadingProgress(++currentStep, totalSteps, "Scanning asset directories..."));
            var assetFontPaths = await ScanAssetDirectoriesAsync();

            // Step 3: Validate and load asset fonts
            progress?.Report(new FontLoadingProgress(++currentStep, totalSteps, "Validating asset fonts..."));
            await LoadAndValidateAssetFontsAsync(assetFontPaths, progress);

            // Step 4: Final validation and cleanup
            progress?.Report(new FontLoadingProgress(++currentStep, totalSteps, "Finalizing font loading..."));
            await FinalizeFontLoadingAsync();

            _fontsLoaded = true;
            _logger.LogInformation("Enhanced font loading completed. Loaded {Count} fonts ({CustomCount} custom fonts)",
                AvailableFonts.Count, _loadedFonts.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during enhanced font loading");
        }
        finally
        {
            _fontLoadingSemaphore.Release();
        }
    }

    /// <summary>
    /// Synchronous wrapper for font loading (backward compatibility)
    /// </summary>
    private void LoadAvailableFonts()
    {
        try
        {
            // Run async method synchronously for backward compatibility
            LoadAvailableFontsAsync().GetAwaiter().GetResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading fonts synchronously");
            // Fallback to basic system fonts
            LoadBasicSystemFonts();
        }
    }

    /// <summary>
    /// Loads basic system fonts as fallback
    /// </summary>
    private void LoadBasicSystemFonts()
    {
        var systemFonts = new[]
        {
            "Arial", "Times New Roman", "Helvetica", "Georgia", "Verdana",
            "Comic Sans MS", "Trebuchet MS", "Impact", "Courier New", "Tahoma",
            "Calibri", "Segoe UI", "Consolas", "Lucida Console"
        };

        foreach (var font in systemFonts)
        {
            if (!AvailableFonts.Contains(font))
            {
                AvailableFonts.Add(font);
            }
        }
    }

    /// <summary>
    /// Loads and validates system fonts
    /// </summary>
    private async Task LoadSystemFontsAsync()
    {
        var systemFonts = new[]
        {
            "Arial", "Times New Roman", "Helvetica", "Georgia", "Verdana",
            "Comic Sans MS", "Trebuchet MS", "Impact", "Courier New", "Tahoma",
            "Calibri", "Segoe UI", "Consolas", "Lucida Console"
        };

        await Task.Run(() =>
        {
            foreach (var font in systemFonts)
            {
                if (ValidateSystemFont(font))
                {
                    AvailableFonts.Add(font);
                    _logger.LogDebug("Validated system font: {FontName}", font);
                }
            }
        });
    }

    /// <summary>
    /// Scans asset directories for font files
    /// </summary>
    private async Task<List<string>> ScanAssetDirectoriesAsync()
    {
        return await Task.Run(() =>
        {
            var fontFiles = new List<string>();
            var assetPaths = GetAssetFontPaths();

            foreach (var assetPath in assetPaths)
            {
                if (Directory.Exists(assetPath))
                {
                    _logger.LogDebug("Scanning font directory: {Path}", assetPath);

                    var files = Directory.GetFiles(assetPath, "*.ttf", SearchOption.AllDirectories)
                        .Concat(Directory.GetFiles(assetPath, "*.otf", SearchOption.AllDirectories))
                        .Concat(Directory.GetFiles(assetPath, "*.ttc", SearchOption.AllDirectories))
                        .ToList();

                    fontFiles.AddRange(files);
                    _logger.LogDebug("Found {Count} font files in {Path}", files.Count, assetPath);
                }
            }

            return fontFiles;
        });
    }

    /// <summary>
    /// Gets potential asset font paths in order of preference
    /// </summary>
    private List<string> GetAssetFontPaths()
    {
        var basePaths = new List<string>();

        // Try multiple potential locations
        var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;

        // Development paths
        basePaths.Add(Path.Combine(baseDirectory, "..", "..", "..", "..", "assets", "Fonts"));
        basePaths.Add(Path.Combine(baseDirectory, "..", "..", "..", "assets", "Fonts"));
        basePaths.Add(Path.Combine(baseDirectory, "..", "..", "assets", "Fonts"));
        basePaths.Add(Path.Combine(baseDirectory, "..", "assets", "Fonts"));
        basePaths.Add(Path.Combine(baseDirectory, "assets", "Fonts"));

        // Production paths
        basePaths.Add(Path.Combine(baseDirectory, "Assets", "Fonts"));
        basePaths.Add(Path.Combine(baseDirectory, "fonts"));
        basePaths.Add(Path.Combine(baseDirectory, "Fonts"));

        // Module-specific paths
        basePaths.Add(Path.Combine(baseDirectory, "..", "..", "..", "..", "modules", "assets", "Fonts"));
        basePaths.Add(Path.Combine(baseDirectory, "..", "..", "..", "modules", "assets", "Fonts"));

        return basePaths.Select(Path.GetFullPath).Distinct().ToList();
    }

    private string CleanFontName(string fontName)
    {
        // Clean up font name for better display
        fontName = fontName.Replace("_", " ").Replace("-", " ");

        // Remove common suffixes but be more selective
        var suffixes = new[] { " Regular", " Bold", " Italic", " Light", " Medium", " Thin",
                              " Black", " Heavy", " SemiBold", " ExtraBold", " (mono)", " (italic)" };

        foreach (var suffix in suffixes)
        {
            if (fontName.EndsWith(suffix, StringComparison.OrdinalIgnoreCase))
            {
                fontName = fontName.Substring(0, fontName.Length - suffix.Length);
                break; // Only remove one suffix
            }
        }

        // Capitalize first letter of each word
        return System.Globalization.CultureInfo.CurrentCulture.TextInfo.ToTitleCase(fontName.ToLower());
    }

    /// <summary>
    /// Validates a system font by attempting to create a typeface
    /// </summary>
    private bool ValidateSystemFont(string fontFamily)
    {
        try
        {
            using var typeface = SKTypeface.FromFamilyName(fontFamily);
            return typeface != null && !string.IsNullOrEmpty(typeface.FamilyName);
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "System font validation failed for: {FontFamily}", fontFamily);
            return false;
        }
    }

    /// <summary>
    /// Loads and validates asset fonts with progress reporting
    /// </summary>
    private async Task LoadAndValidateAssetFontsAsync(List<string> fontFiles, IProgress<FontLoadingProgress>? progress = null)
    {
        var validatedCount = 0;
        var totalFiles = fontFiles.Count;

        await Task.Run(() =>
        {
            foreach (var fontFile in fontFiles)
            {
                try
                {
                    if (ValidateAndLoadAssetFont(fontFile))
                    {
                        validatedCount++;
                    }

                    // Report progress every 10 fonts or at the end
                    if (validatedCount % 10 == 0 || validatedCount == totalFiles)
                    {
                        progress?.Report(new FontLoadingProgress(3, 4,
                            $"Validated {validatedCount}/{totalFiles} asset fonts..."));
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to process font file: {FontFile}", fontFile);
                }
            }
        });

        _logger.LogInformation("Validated {ValidatedCount} out of {TotalCount} asset fonts", validatedCount, totalFiles);
    }

    /// <summary>
    /// Validates and loads a single asset font file
    /// </summary>
    private bool ValidateAndLoadAssetFont(string fontFile)
    {
        SKData? fontData = null;
        SKTypeface? typeface = null;

        try
        {
            // Validate file exists and is readable
            if (!File.Exists(fontFile) || new FileInfo(fontFile).Length == 0)
            {
                return false;
            }

            // Load the font file and create typeface
            fontData = SKData.Create(fontFile);
            if (fontData == null) return false;

            typeface = SKTypeface.FromData(fontData);
            if (typeface == null) return false;

            // Test font rendering capability
            if (!TestFontRendering(typeface))
            {
                return false;
            }

            // Get font name
            var fontName = GetValidFontName(typeface, fontFile);
            if (string.IsNullOrWhiteSpace(fontName)) return false;

            // Check for duplicates
            if (AvailableFonts.Contains(fontName))
            {
                return false;
            }

            // Successfully validated - add to collections
            AvailableFonts.Add(fontName);
            _loadedFonts[fontName] = typeface;
            _fontFilePaths[fontName] = fontFile;

            _logger.LogDebug("Successfully loaded and validated font: {FontName} from {FontFile}", fontName, fontFile);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Font validation failed for: {FontFile}", fontFile);
            typeface?.Dispose();
            return false;
        }
        finally
        {
            fontData?.Dispose();
        }
    }

    /// <summary>
    /// Tests font rendering capability by attempting to render test text
    /// </summary>
    private bool TestFontRendering(SKTypeface typeface)
    {
        var paint = _paintPool.Get();
        try
        {
            paint.Typeface = typeface;
            paint.TextSize = 24;
            paint.IsAntialias = true;

            // Test with common clock characters
            var testText = "0123456789:. AM PM";
            var bounds = new SKRect();
            var width = paint.MeasureText(testText, ref bounds);

            // Font is valid if it can measure text and has reasonable bounds
            return width > 0 && bounds.Width > 0 && bounds.Height > 0;
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Font rendering test failed for typeface: {FamilyName}", typeface.FamilyName);
            return false;
        }
        finally
        {
            _paintPool.Return(paint);
        }
    }

    /// <summary>
    /// Gets a valid font name from typeface or filename
    /// </summary>
    private string GetValidFontName(SKTypeface typeface, string fontFile)
    {
        // Try to get the actual font family name from the typeface
        var fontName = typeface.FamilyName;

        // If that fails, use filename with cleanup
        if (string.IsNullOrWhiteSpace(fontName))
        {
            fontName = Path.GetFileNameWithoutExtension(fontFile);
        }

        return CleanFontName(fontName);
    }

    /// <summary>
    /// Finalizes font loading process
    /// </summary>
    private async Task FinalizeFontLoadingAsync()
    {
        await Task.Run(() =>
        {
            // Add any missing essential fonts that should always be available
            var essentialFonts = new[] { "Arial", "Times New Roman", "Calibri" };
            foreach (var font in essentialFonts)
            {
                if (!AvailableFonts.Contains(font))
                {
                    AvailableFonts.Add(font);
                }
            }

            // Sort fonts alphabetically for better UX
            var sortedFonts = AvailableFonts.OrderBy(f => f).ToList();
            AvailableFonts.Clear();
            foreach (var font in sortedFonts)
            {
                AvailableFonts.Add(font);
            }
        });
    }

    /// <summary>
    /// Public method to reload fonts asynchronously with progress
    /// Last Updated: 2025-01-11 18:30:00 UTC
    /// </summary>
    /// <param name="progress">Progress reporting interface</param>
    [TestableMethod("FontLoading", IncludePerformanceTests = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 2000)]
    public async Task ReloadFontsAsync(IProgress<FontLoadingProgress>? progress = null)
    {
        _fontsLoaded = false;
        AvailableFonts.Clear();

        // Dispose existing loaded fonts
        foreach (var typeface in _loadedFonts.Values)
        {
            typeface?.Dispose();
        }
        _loadedFonts.Clear();
        _fontFilePaths.Clear();

        await LoadAvailableFontsAsync(progress);
    }

    private void SetupPreviewTimer()
    {
        _previewUpdateTimer = new DispatcherTimer
        {
            Interval = TimeSpan.FromSeconds(1)
        };
        _previewUpdateTimer.Tick += (s, e) => PreviewUpdateRequested?.Invoke();
        _previewUpdateTimer.Start();
    }

    protected override void OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs e)
    {
        base.OnPropertyChanged(e);

        // Trigger HasBackgroundImage when BackgroundImagePath changes
        if (e.PropertyName == nameof(BackgroundImagePath))
        {
            OnPropertyChanged(nameof(HasBackgroundImage));
        }

        // Trigger preview update when any property changes
        PreviewUpdateRequested?.Invoke();
    }

    private void DrawGlowEffect(SKCanvas canvas, string text, float x, float y, SKPaint basePaint)
    {
        try
        {
            // Create multiple layers for a proper glow effect
            var glowLayers = new[] { (float)GlowRadius * 1.5f, (float)GlowRadius, (float)GlowRadius * 0.7f, (float)GlowRadius * 0.4f };
            var glowOpacities = new[] { 0.2f, 0.3f, 0.5f, 0.7f };

            for (int i = 0; i < glowLayers.Length; i++)
            {
                var glowPaint = _paintPool.Get();
                try
                {
                    // Copy properties from base paint
                    glowPaint.TextSize = basePaint.TextSize;
                    glowPaint.Typeface = basePaint.Typeface;
                    glowPaint.IsAntialias = basePaint.IsAntialias;

                    glowPaint.MaskFilter = SKMaskFilter.CreateBlur(SKBlurStyle.Normal, glowLayers[i]);
                    glowPaint.Color = ToSKColor(GlowColor).WithAlpha((byte)(255 * glowOpacities[i]));
                    canvas.DrawText(text, x, y, glowPaint);
                }
                finally
                {
                    _paintPool.Return(glowPaint);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error drawing glow effect");
        }
    }

    private void Draw3DText(SKCanvas canvas, string text, float x, float y, SKPaint basePaint)
    {
        try
        {
            var depth = (float)TextDepth;
            var bevel = (float)BevelSize;

            // Create 3D effect by drawing multiple layers
            for (int i = (int)depth; i >= 0; i--)
            {
                var layerPaint = _paintPool.Get();
                try
                {
                    // Copy properties from base paint
                    layerPaint.TextSize = basePaint.TextSize;
                    layerPaint.Typeface = basePaint.Typeface;
                    layerPaint.IsAntialias = basePaint.IsAntialias;

                    // Calculate depth color (darker as we go deeper)
                    var depthFactor = 1.0f - (i / depth * 0.6f); // Darken by up to 60%
                    var baseColor = ToSKColor(Text3DColor);

                    // Apply lighting if enabled
                    if (EnableLighting)
                    {
                        depthFactor = ApplyLighting(depthFactor, i, depth);
                    }

                    layerPaint.Color = new SKColor(
                        (byte)(baseColor.Red * depthFactor),
                        (byte)(baseColor.Green * depthFactor),
                        (byte)(baseColor.Blue * depthFactor),
                        baseColor.Alpha);

                    // Draw the layer with offset
                    var offsetX = x + (i * 0.5f);
                    var offsetY = y + (i * 0.5f);

                    // Add bevel effect for front layers
                    if (i <= bevel)
                    {
                        var bevelFactor = 1.0f + (bevel - i) * 0.2f;
                        layerPaint.Color = layerPaint.Color.WithAlpha((byte)(255 * bevelFactor));
                    }

                    canvas.DrawText(text, offsetX, offsetY, layerPaint);
                }
                finally
                {
                    _paintPool.Return(layerPaint);
                }
            }

            // Draw the front face
            var frontPaint = _paintPool.Get();
            try
            {
                // Copy properties from base paint
                frontPaint.TextSize = basePaint.TextSize;
                frontPaint.Typeface = basePaint.Typeface;
                frontPaint.IsAntialias = basePaint.IsAntialias;

                frontPaint.Color = ToSKColor(Text3DColor);
                canvas.DrawText(text, x, y, frontPaint);
            }
            finally
            {
                _paintPool.Return(frontPaint);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error drawing 3D text");
        }
    }

    private float ApplyLighting(float baseFactor, int layer, float totalDepth)
    {
        try
        {
            // Convert angles to radians
            var angleXRad = LightAngleX * Math.PI / 180.0;
            var angleYRad = LightAngleY * Math.PI / 180.0;

            // Calculate light direction
            var lightX = Math.Cos(angleXRad);
            var lightY = Math.Sin(angleYRad);
            var lightZ = Math.Sin(angleXRad);

            // Calculate surface normal (simplified)
            var normalX = 0.0;
            var normalY = 0.0;
            var normalZ = 1.0;

            // Calculate dot product for lighting intensity
            var dotProduct = lightX * normalX + lightY * normalY + lightZ * normalZ;
            var lightingFactor = Math.Max(0.2, dotProduct) * LightIntensity;

            // Apply depth-based lighting variation
            var depthFactor = 1.0f - (layer / totalDepth * 0.3f);

            return (float)(baseFactor * lightingFactor * depthFactor);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying lighting");
            return baseFactor;
        }
    }

    /// <summary>
    /// Applies enhanced lighting with multiple light sources and advanced features
    /// </summary>
    private float ApplyEnhancedLighting(float baseFactor, int layer, float totalDepth)
    {
        try
        {
            float totalLighting = 0f;

            // Apply primary light
            var primaryLighting = CalculateLightContribution(LightAngleX, LightAngleY, LightIntensity);
            totalLighting += primaryLighting;

            // Apply secondary light if enabled
            if (EnableMultipleLights)
            {
                var secondaryLighting = CalculateLightContribution(SecondaryLightAngleX, SecondaryLightAngleY, SecondaryLightIntensity);
                totalLighting += secondaryLighting;
            }

            // Apply tertiary light if enabled
            if (EnableTertiaryLight)
            {
                var tertiaryLighting = CalculateLightContribution(TertiaryLightAngleX, TertiaryLightAngleY, TertiaryLightIntensity);
                totalLighting += tertiaryLighting;
            }

            // Apply quaternary light if enabled
            if (EnableQuaternaryLight)
            {
                var quaternaryLighting = CalculateLightContribution(QuaternaryLightAngleX, QuaternaryLightAngleY, QuaternaryLightIntensity);
                totalLighting += quaternaryLighting;
            }

            // Apply light blending mode
            if (LegacyLightBlendingMode < 1.0)
            {
                // Additive blending (0.0) to multiplicative blending (1.0)
                var additiveComponent = totalLighting * (1.0f - (float)LegacyLightBlendingMode);
                var multiplicativeComponent = totalLighting * (float)LegacyLightBlendingMode;
                totalLighting = additiveComponent + multiplicativeComponent;
            }

            // Apply ambient lighting
            totalLighting += (float)AmbientIntensity;

            // Apply ambient occlusion if enabled
            if (EnableAmbientOcclusion)
            {
                var occlusionFactor = CalculateAmbientOcclusion(layer, totalDepth);
                totalLighting *= occlusionFactor;
            }

            // Apply material properties
            totalLighting = ApplyMaterialProperties(totalLighting);

            // Apply depth-based lighting variation
            var depthFactor = 1.0f - (layer / totalDepth * 0.3f);

            return Math.Max(0.1f, baseFactor * totalLighting * depthFactor);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying enhanced lighting");
            return baseFactor;
        }
    }

    private float CalculateLightContribution(double angleX, double angleY, double intensity)
    {
        // Convert angles to radians
        var angleXRad = angleX * Math.PI / 180.0;
        var angleYRad = angleY * Math.PI / 180.0;

        // Calculate light direction
        var lightX = Math.Cos(angleXRad);
        var lightY = Math.Sin(angleYRad);
        var lightZ = Math.Sin(angleXRad);

        // Calculate surface normal (simplified)
        var normalX = 0.0;
        var normalY = 0.0;
        var normalZ = 1.0;

        // Calculate dot product for lighting intensity
        var dotProduct = lightX * normalX + lightY * normalY + lightZ * normalZ;
        return (float)(Math.Max(0.0, dotProduct) * intensity);
    }

    private float CalculateAmbientOcclusion(int layer, float totalDepth)
    {
        // Simple ambient occlusion calculation based on depth
        var depthRatio = layer / totalDepth;
        var occlusionAmount = depthRatio * AmbientOcclusionIntensity;
        return (float)(1.0 - occlusionAmount);
    }

    private float ApplyMaterialProperties(float lighting)
    {
        return Material switch
        {
            SurfaceMaterial.Metallic => lighting * (1.0f + (float)Metallic * 0.5f),
            SurfaceMaterial.Chrome => lighting * 1.3f,
            SurfaceMaterial.Gold => lighting * 1.2f,
            SurfaceMaterial.Copper => lighting * 1.1f,
            SurfaceMaterial.Glossy => lighting * (1.0f + (1.0f - (float)Roughness) * 0.3f),
            SurfaceMaterial.Glass => lighting * 0.9f,
            SurfaceMaterial.Plastic => lighting * 1.0f,
            SurfaceMaterial.Rubber => lighting * 0.8f,
            _ => lighting // Matte
        };
    }

    /// <summary>
    /// Draws glow effect with independent opacity control
    /// </summary>
    private void DrawGlowEffectWithOpacity(SKCanvas canvas, string text, float x, float y, SKPaint basePaint)
    {
        try
        {
            // Create multiple layers for a proper glow effect with opacity
            var glowLayers = new[] { (float)GlowRadius * 1.5f, (float)GlowRadius, (float)GlowRadius * 0.7f, (float)GlowRadius * 0.4f };
            var glowOpacities = new[] { 0.2f, 0.3f, 0.5f, 0.7f };

            for (int i = 0; i < glowLayers.Length; i++)
            {
                var glowPaint = _paintPool.Get();
                try
                {
                    // Copy properties from base paint
                    glowPaint.TextSize = basePaint.TextSize;
                    glowPaint.Typeface = basePaint.Typeface;
                    glowPaint.IsAntialias = basePaint.IsAntialias;

                    glowPaint.MaskFilter = SKMaskFilter.CreateBlur(SKBlurStyle.Normal, glowLayers[i]);

                    // Apply glow color with layer opacity and text opacity
                    var glowColor = ToSKColor(GlowColor);
                    var finalOpacity = glowOpacities[i] * (float)TextOpacity;
                    glowPaint.Color = glowColor.WithAlpha((byte)(255 * finalOpacity));

                    canvas.DrawText(text, x, y, glowPaint);
                }
                finally
                {
                    _paintPool.Return(glowPaint);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error drawing glow effect with opacity");
        }
    }

    /// <summary>
    /// Draws shadow with independent opacity control and enhanced offset range
    /// </summary>
    private void DrawShadowWithOpacity(SKCanvas canvas, string text, float x, float y, SKPaint basePaint)
    {
        var shadowPaint = _paintPool.Get();
        try
        {
            // Copy properties from base paint
            shadowPaint.TextSize = basePaint.TextSize;
            shadowPaint.Typeface = basePaint.Typeface;
            shadowPaint.IsAntialias = basePaint.IsAntialias;

            // Apply shadow blur
            if (ShadowBlur > 0)
            {
                shadowPaint.MaskFilter = SKMaskFilter.CreateBlur(SKBlurStyle.Normal, (float)ShadowBlur);
            }

            // Apply shadow color with text opacity
            var shadowColor = ToSKColor(ShadowColor);
            var finalOpacity = shadowColor.Alpha / 255.0f * (float)TextOpacity;
            shadowPaint.Color = shadowColor.WithAlpha((byte)(255 * finalOpacity));

            // Apply enhanced shadow offset (supports up to 3x previous range)
            var shadowX = x + (float)ShadowOffsetX;
            var shadowY = y + (float)ShadowOffsetY;

            canvas.DrawText(text, shadowX, shadowY, shadowPaint);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error drawing shadow with opacity");
        }
        finally
        {
            _paintPool.Return(shadowPaint);
        }
    }

    /// <summary>
    /// Draws stroke with independent opacity control
    /// </summary>
    private void DrawStrokeWithOpacity(SKCanvas canvas, string text, float x, float y, SKPaint basePaint)
    {
        var strokePaint = _paintPool.Get();
        try
        {
            // Copy properties from base paint
            strokePaint.TextSize = basePaint.TextSize;
            strokePaint.Typeface = basePaint.Typeface;
            strokePaint.IsAntialias = basePaint.IsAntialias;

            strokePaint.Style = SKPaintStyle.Stroke;
            strokePaint.StrokeWidth = (float)StrokeWidth;

            // Apply stroke color with stroke opacity
            var strokeColor = ToSKColor(StrokeColor);
            var finalOpacity = strokeColor.Alpha / 255.0f * (float)StrokeOpacity;
            strokePaint.Color = strokeColor.WithAlpha((byte)(255 * finalOpacity));

            canvas.DrawText(text, x, y, strokePaint);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error drawing stroke with opacity");
        }
        finally
        {
            _paintPool.Return(strokePaint);
        }
    }

    /// <summary>
    /// Draws regular text with independent opacity control
    /// </summary>
    private void DrawTextWithOpacity(SKCanvas canvas, string text, float x, float y, SKPaint basePaint)
    {
        var textPaint = _paintPool.Get();
        try
        {
            // Copy properties from base paint
            textPaint.TextSize = basePaint.TextSize;
            textPaint.Typeface = basePaint.Typeface;
            textPaint.IsAntialias = basePaint.IsAntialias;

            // Apply text color with text opacity
            var textColor = ToSKColor(TextColor);
            var finalOpacity = textColor.Alpha / 255.0f * (float)TextOpacity;
            textPaint.Color = textColor.WithAlpha((byte)(255 * finalOpacity));

            canvas.DrawText(text, x, y, textPaint);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error drawing text with opacity");
        }
        finally
        {
            _paintPool.Return(textPaint);
        }
    }

    /// <summary>
    /// Draws 3D text with enhanced lighting and materials using the Enhanced3DTextRenderer
    /// </summary>
    private async void Draw3DTextWithOpacity(SKCanvas canvas, string text, float x, float y, SKPaint basePaint)
    {
        try
        {
            // Use enhanced 3D renderer if available and advanced features are enabled
            if (_enhanced3DRenderer != null && (EnableMultipleLights || Material != SurfaceMaterial.Matte || EnableAmbientOcclusion || EnableEnvironmentReflection))
            {
                var settings = CreateClockSettings();
                var enhanced3DBitmap = await _enhanced3DRenderer.RenderEnhanced3DTextWithOpacityAsync(text, settings, basePaint, TextOpacity);

                if (enhanced3DBitmap != null)
                {
                    canvas.DrawBitmap(enhanced3DBitmap, x, y);
                    enhanced3DBitmap.Dispose();
                    return;
                }
            }

            // Fall back to traditional 3D rendering
            var depth = (float)TextDepth;
            var bevel = (float)BevelSize;

            // Create 3D effect by drawing multiple layers with opacity
            for (int i = (int)depth; i >= 0; i--)
            {
                var layerPaint = _paintPool.Get();
                try
                {
                    // Copy properties from base paint
                    layerPaint.TextSize = basePaint.TextSize;
                    layerPaint.Typeface = basePaint.Typeface;
                    layerPaint.IsAntialias = basePaint.IsAntialias;

                    // Calculate depth color (darker as we go deeper)
                    var depthFactor = 1.0f - (i / depth * 0.6f); // Darken by up to 60%
                    var baseColor = ToSKColor(Text3DColor);

                    // Apply enhanced lighting if enabled
                    if (EnableLighting)
                    {
                        depthFactor = ApplyEnhancedLighting(depthFactor, i, depth);
                    }

                    // Apply text opacity to 3D layers
                    var finalOpacity = baseColor.Alpha / 255.0f * (float)TextOpacity;
                    layerPaint.Color = new SKColor(
                        (byte)(baseColor.Red * depthFactor),
                        (byte)(baseColor.Green * depthFactor),
                        (byte)(baseColor.Blue * depthFactor),
                        (byte)(255 * finalOpacity));

                    // Draw the layer with offset
                    var offsetX = x + (i * 0.5f);
                    var offsetY = y + (i * 0.5f);

                    // Add bevel effect for front layers
                    if (i <= bevel)
                    {
                        var bevelFactor = 1.0f + (bevel - i) * 0.2f;
                        var currentAlpha = layerPaint.Color.Alpha;
                        layerPaint.Color = layerPaint.Color.WithAlpha((byte)(currentAlpha * bevelFactor));
                    }

                    canvas.DrawText(text, offsetX, offsetY, layerPaint);
                }
                finally
                {
                    _paintPool.Return(layerPaint);
                }
            }

            // Draw the front face with text opacity
            var frontPaint = _paintPool.Get();
            try
            {
                // Copy properties from base paint
                frontPaint.TextSize = basePaint.TextSize;
                frontPaint.Typeface = basePaint.Typeface;
                frontPaint.IsAntialias = basePaint.IsAntialias;

                var frontColor = ToSKColor(Text3DColor);
                var frontOpacity = frontColor.Alpha / 255.0f * (float)TextOpacity;
                frontPaint.Color = frontColor.WithAlpha((byte)(255 * frontOpacity));
                canvas.DrawText(text, x, y, frontPaint);
            }
            finally
            {
                _paintPool.Return(frontPaint);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error drawing enhanced 3D text with opacity");
        }
    }

    /// <summary>
    /// Applies predefined lighting preset configurations
    /// </summary>
    private void ApplyLightingPresetConfiguration(LightingPreset preset)
    {
        try
        {
            switch (preset)
            {
                case LightingPreset.Studio:
                    ApplyStudioLighting();
                    break;
                case LightingPreset.Natural:
                    ApplyNaturalLighting();
                    break;
                case LightingPreset.Dramatic:
                    ApplyDramaticLighting();
                    break;
                case LightingPreset.Soft:
                    ApplySoftLighting();
                    break;
                case LightingPreset.Cinematic:
                    ApplyCinematicLighting();
                    break;
                case LightingPreset.Neon:
                    ApplyNeonLighting();
                    break;
                case LightingPreset.Sunset:
                    ApplySunsetLighting();
                    break;
                case LightingPreset.Moonlight:
                    ApplyMoonlightLighting();
                    break;
                case LightingPreset.HighKey:
                    ApplyHighKeyLighting();
                    break;
                case LightingPreset.LowKey:
                    ApplyLowKeyLighting();
                    break;
                case LightingPreset.Custom:
                default:
                    // Custom preset - don't change anything
                    break;
            }

            _logger.LogDebug("Applied lighting preset: {Preset}", preset);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying lighting preset configuration: {Preset}", preset);
        }
    }

    /// <summary>
    /// Applies professional studio lighting setup
    /// </summary>
    private void ApplyStudioLighting()
    {
        // Key light (primary)
        EnableLighting = true;
        LightIntensity = 1.2;
        LightAngleX = 45;
        LightAngleY = -30;
        LightColor = System.Windows.Media.Brushes.White;

        // Fill light (secondary)
        EnableMultipleLights = true;
        SecondaryLightIntensity = 0.6;
        SecondaryLightAngleX = -45;
        SecondaryLightAngleY = 30;
        SecondaryLightColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(255, 248, 220)); // Warm white
        SecondaryLightType = LightType.Directional;

        // Rim light (tertiary)
        EnableTertiaryLight = true;
        TertiaryLightIntensity = 0.4;
        TertiaryLightAngleX = 135;
        TertiaryLightAngleY = -15;
        TertiaryLightColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(173, 216, 230)); // Light blue
        TertiaryLightType = LightType.Directional;

        // Ambient
        AmbientIntensity = 0.15;
        EnableAmbientOcclusion = true;
        AmbientOcclusionIntensity = 0.2;
    }

    /// <summary>
    /// Applies natural outdoor lighting
    /// </summary>
    private void ApplyNaturalLighting()
    {
        // Sun (primary)
        EnableLighting = true;
        LightIntensity = 1.0;
        LightAngleX = 60;
        LightAngleY = -45;
        LightColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(255, 255, 240)); // Ivory

        // Sky fill (secondary)
        EnableMultipleLights = true;
        SecondaryLightIntensity = 0.4;
        SecondaryLightAngleX = -30;
        SecondaryLightAngleY = 60;
        SecondaryLightColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(135, 206, 235)); // Sky blue
        SecondaryLightType = LightType.Area;

        // Disable additional lights
        EnableTertiaryLight = false;
        EnableQuaternaryLight = false;

        // Natural ambient
        AmbientIntensity = 0.3;
        EnableAmbientOcclusion = true;
        AmbientOcclusionIntensity = 0.15;
    }

    /// <summary>
    /// Applies dramatic high-contrast lighting
    /// </summary>
    private void ApplyDramaticLighting()
    {
        // Strong key light
        EnableLighting = true;
        LightIntensity = 1.5;
        LightAngleX = 75;
        LightAngleY = -60;
        LightColor = System.Windows.Media.Brushes.White;

        // Minimal fill
        EnableMultipleLights = true;
        SecondaryLightIntensity = 0.2;
        SecondaryLightAngleX = -45;
        SecondaryLightAngleY = 45;
        SecondaryLightColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(100, 100, 150)); // Cool dim
        SecondaryLightType = LightType.Directional;

        // Strong rim light
        EnableTertiaryLight = true;
        TertiaryLightIntensity = 0.8;
        TertiaryLightAngleX = 150;
        TertiaryLightAngleY = -30;
        TertiaryLightColor = System.Windows.Media.Brushes.White;
        TertiaryLightType = LightType.Spot;

        // Low ambient for drama
        AmbientIntensity = 0.05;
        EnableAmbientOcclusion = true;
        AmbientOcclusionIntensity = 0.4;
    }

    /// <summary>
    /// Applies soft even lighting for readability
    /// </summary>
    private void ApplySoftLighting()
    {
        // Soft key light
        EnableLighting = true;
        LightIntensity = 0.8;
        LightAngleX = 30;
        LightAngleY = -30;
        LightColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(255, 248, 220)); // Warm white

        // Strong fill light
        EnableMultipleLights = true;
        SecondaryLightIntensity = 0.7;
        SecondaryLightAngleX = -30;
        SecondaryLightAngleY = 30;
        SecondaryLightColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(248, 248, 255)); // Cool white
        SecondaryLightType = LightType.Area;

        // Soft rim light
        EnableTertiaryLight = true;
        TertiaryLightIntensity = 0.3;
        TertiaryLightAngleX = 120;
        TertiaryLightAngleY = -15;
        TertiaryLightColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(255, 248, 220)); // Warm white
        TertiaryLightType = LightType.Area;

        // High ambient for softness
        AmbientIntensity = 0.4;
        EnableAmbientOcclusion = false;
    }

    /// <summary>
    /// Applies cinematic lighting with strong directional bias
    /// </summary>
    private void ApplyCinematicLighting()
    {
        // Strong directional key
        EnableLighting = true;
        LightIntensity = 1.3;
        LightAngleX = 60;
        LightAngleY = -45;
        LightColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(255, 220, 177)); // Warm cinematic

        // Subtle fill
        EnableMultipleLights = true;
        SecondaryLightIntensity = 0.3;
        SecondaryLightAngleX = -60;
        SecondaryLightAngleY = 60;
        SecondaryLightColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(173, 216, 230)); // Cool blue
        SecondaryLightType = LightType.Directional;

        // Strong rim for separation
        EnableTertiaryLight = true;
        TertiaryLightIntensity = 0.9;
        TertiaryLightAngleX = 135;
        TertiaryLightAngleY = -20;
        TertiaryLightColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(255, 165, 0)); // Orange
        TertiaryLightType = LightType.Spot;

        // Low ambient for contrast
        AmbientIntensity = 0.1;
        EnableAmbientOcclusion = true;
        AmbientOcclusionIntensity = 0.3;
    }

    /// <summary>
    /// Applies neon-style lighting with vibrant colors
    /// </summary>
    private void ApplyNeonLighting()
    {
        // Bright cyan primary
        EnableLighting = true;
        LightIntensity = 1.0;
        LightAngleX = 0;
        LightAngleY = 0;
        LightColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(0, 255, 255)); // Cyan

        // Magenta secondary
        EnableMultipleLights = true;
        SecondaryLightIntensity = 0.8;
        SecondaryLightAngleX = 90;
        SecondaryLightAngleY = 0;
        SecondaryLightColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(255, 0, 255)); // Magenta
        SecondaryLightType = LightType.Point;

        // Electric blue tertiary
        EnableTertiaryLight = true;
        TertiaryLightIntensity = 0.6;
        TertiaryLightAngleX = -90;
        TertiaryLightAngleY = 0;
        TertiaryLightColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(0, 100, 255)); // Electric blue
        TertiaryLightType = LightType.Point;

        // Purple quaternary
        EnableQuaternaryLight = true;
        QuaternaryLightIntensity = 0.4;
        QuaternaryLightAngleX = 45;
        QuaternaryLightAngleY = 45;
        QuaternaryLightColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(128, 0, 128)); // Purple
        QuaternaryLightType = LightType.Area;

        // Dark ambient for neon effect
        AmbientIntensity = 0.05;
        AmbientColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(20, 20, 40)); // Dark blue
        EnableAmbientOcclusion = false;
    }

    /// <summary>
    /// Applies sunset/sunrise warm lighting
    /// </summary>
    private void ApplySunsetLighting()
    {
        // Warm orange sun
        EnableLighting = true;
        LightIntensity = 1.1;
        LightAngleX = 75;
        LightAngleY = -15;
        LightColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(255, 140, 0)); // Dark orange

        // Warm fill
        EnableMultipleLights = true;
        SecondaryLightIntensity = 0.5;
        SecondaryLightAngleX = -45;
        SecondaryLightAngleY = 30;
        SecondaryLightColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(255, 165, 79)); // Light orange
        SecondaryLightType = LightType.Area;

        // Purple sky reflection
        EnableTertiaryLight = true;
        TertiaryLightIntensity = 0.3;
        TertiaryLightAngleX = -120;
        TertiaryLightAngleY = 45;
        TertiaryLightColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(147, 112, 219)); // Medium purple
        TertiaryLightType = LightType.Area;

        // Warm ambient
        AmbientIntensity = 0.25;
        AmbientColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(255, 228, 196)); // Bisque
        EnableAmbientOcclusion = true;
        AmbientOcclusionIntensity = 0.2;
    }

    /// <summary>
    /// Applies cool moonlight-style lighting
    /// </summary>
    private void ApplyMoonlightLighting()
    {
        // Cool blue moon
        EnableLighting = true;
        LightIntensity = 0.7;
        LightAngleX = 45;
        LightAngleY = -60;
        LightColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(173, 216, 230)); // Light blue

        // Subtle fill
        EnableMultipleLights = true;
        SecondaryLightIntensity = 0.2;
        SecondaryLightAngleX = -30;
        SecondaryLightAngleY = 45;
        SecondaryLightColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(230, 230, 250)); // Lavender
        SecondaryLightType = LightType.Area;

        // Disable additional lights
        EnableTertiaryLight = false;
        EnableQuaternaryLight = false;

        // Cool dark ambient
        AmbientIntensity = 0.1;
        AmbientColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(25, 25, 112)); // Midnight blue
        EnableAmbientOcclusion = true;
        AmbientOcclusionIntensity = 0.3;
    }

    /// <summary>
    /// Applies high-key bright lighting
    /// </summary>
    private void ApplyHighKeyLighting()
    {
        // Bright key light
        EnableLighting = true;
        LightIntensity = 1.0;
        LightAngleX = 30;
        LightAngleY = -30;
        LightColor = System.Windows.Media.Brushes.White;

        // Strong fill lights
        EnableMultipleLights = true;
        SecondaryLightIntensity = 0.8;
        SecondaryLightAngleX = -30;
        SecondaryLightAngleY = 30;
        SecondaryLightColor = System.Windows.Media.Brushes.White;
        SecondaryLightType = LightType.Area;

        EnableTertiaryLight = true;
        TertiaryLightIntensity = 0.6;
        TertiaryLightAngleX = 90;
        TertiaryLightAngleY = 0;
        TertiaryLightColor = System.Windows.Media.Brushes.White;
        TertiaryLightType = LightType.Area;

        EnableQuaternaryLight = true;
        QuaternaryLightIntensity = 0.4;
        QuaternaryLightAngleX = -90;
        QuaternaryLightAngleY = 0;
        QuaternaryLightColor = System.Windows.Media.Brushes.White;
        QuaternaryLightType = LightType.Area;

        // Very bright ambient
        AmbientIntensity = 0.6;
        AmbientColor = System.Windows.Media.Brushes.White;
        EnableAmbientOcclusion = false;
    }

    /// <summary>
    /// Applies low-key moody lighting
    /// </summary>
    private void ApplyLowKeyLighting()
    {
        // Single dramatic light
        EnableLighting = true;
        LightIntensity = 1.2;
        LightAngleX = 60;
        LightAngleY = -45;
        LightColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(255, 248, 220)); // Warm white

        // Minimal fill
        EnableMultipleLights = true;
        SecondaryLightIntensity = 0.15;
        SecondaryLightAngleX = -90;
        SecondaryLightAngleY = 60;
        SecondaryLightColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(100, 100, 150)); // Cool dim
        SecondaryLightType = LightType.Directional;

        // Disable additional lights
        EnableTertiaryLight = false;
        EnableQuaternaryLight = false;

        // Very low ambient
        AmbientIntensity = 0.02;
        AmbientColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(10, 10, 10)); // Almost black
        EnableAmbientOcclusion = true;
        AmbientOcclusionIntensity = 0.5;
    }

    private SKTypeface GetTypeface(string fontFamily)
    {
        // Try to get custom loaded font first
        if (_loadedFonts.TryGetValue(fontFamily, out var customTypeface))
        {
            return customTypeface;
        }

        // Fall back to system font
        return SKTypeface.FromFamilyName(fontFamily) ?? SKTypeface.Default;
    }

    // Selection Tools and AI Integration Helper Methods
    /// <summary>
    /// Initializes the selection tools engine for advanced text positioning
    /// Last Updated: 2025-01-09 23:50:00 UTC
    /// </summary>
    private async Task InitializeSelectionToolsAsync()
    {
        try
        {
            if (!_selectionToolsEngine.IsInitialized)
            {
                await _selectionToolsEngine.InitializeAsync();
                _logger.LogInformation("Selection tools engine initialized");
            }

            // Configure selection tools for clock text elements
            _selectionToolsEngine.SetCanvasSize(ClockWidth, ClockHeight);
            _selectionToolsEngine.SelectionChanged += OnSelectionChanged;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error initializing selection tools");
            throw;
        }
    }

    /// <summary>
    /// Handles selection changes from the selection tools engine
    /// Last Updated: 2025-01-09 23:50:00 UTC
    /// </summary>
    private void OnSelectionChanged(object? sender, SelectionChangedEventArgs e)
    {
        try
        {
            if (e.Selection != null && e.Selection.Bounds.Width > 0 && e.Selection.Bounds.Height > 0)
            {
                _logger.LogDebug("Selection changed: {Bounds}", e.Selection.Bounds);

                // Update text positioning based on selection
                UpdateTextPositionFromSelection(e.Selection);

                PreviewUpdateRequested?.Invoke();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling selection change");
        }
    }

    /// <summary>
    /// Updates text positioning based on selection bounds
    /// Last Updated: 2025-01-09 23:50:00 UTC
    /// </summary>
    private void UpdateTextPositionFromSelection(SelectionData selection)
    {
        try
        {
            // Calculate center of selection for text positioning
            var centerX = selection.Bounds.X + (selection.Bounds.Width / 2);
            var centerY = selection.Bounds.Y + (selection.Bounds.Height / 2);

            // Update date positioning if date is visible
            if (ShowDate)
            {
                DateOffsetX = centerX - (ClockWidth / 2);
                DateOffsetY = centerY - (ClockHeight / 2) + 50; // Offset below time
            }

            _logger.LogDebug("Updated text position from selection: X={X}, Y={Y}", centerX, centerY);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating text position from selection");
        }
    }

    /// <summary>
    /// Samples color from desktop wallpaper using eye dropper functionality
    /// Last Updated: 2025-01-09 23:50:00 UTC
    /// </summary>
    private async Task<SolidColorBrush?> SampleDesktopColorAsync()
    {
        try
        {
            // Use selection tools engine's eye dropper functionality
            var sampledColor = await _selectionToolsEngine.SampleColorAsync(0, 0); // Sample at cursor position

            if (sampledColor != null)
            {
                var wpfColor = System.Windows.Media.Color.FromArgb(
                    sampledColor.Value.Alpha,
                    sampledColor.Value.Red,
                    sampledColor.Value.Green,
                    sampledColor.Value.Blue);

                return new SolidColorBrush(wpfColor);
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sampling desktop color");
            return null;
        }
    }

    /// <summary>
    /// Applies sampled color to the current color selection
    /// Last Updated: 2025-01-09 23:50:00 UTC
    /// </summary>
    private void ApplySampledColorToCurrentSelection()
    {
        try
        {
            if (SampledColor != null)
            {
                // Apply to text color by default
                TextColor = SampledColor;
                _logger.LogInformation("Applied sampled color to text color");

                PreviewUpdateRequested?.Invoke();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying sampled color");
        }
    }

    /// <summary>
    /// Creates canvas analysis data for AI processing
    /// Last Updated: 2025-01-09 23:50:00 UTC
    /// </summary>
    private CanvasAnalysisData CreateCanvasAnalysisData()
    {
        try
        {
            return new CanvasAnalysisData
            {
                CanvasSize = new System.Drawing.Size(ClockWidth, ClockHeight),
                LayerCount = ClockLayers.Count,
                EstimatedMemoryUsage = CalculateEstimatedMemoryUsage(),
                HasLightingData = EnableLighting || EnableMultipleLights,
                AverageBrightness = CalculateAverageBrightness(),
                HasComplexBrushwork = Enable3D || EnableGlow || EnableShadow,
                HasFaceDetection = false, // Clock doesn't typically have faces
                HasHorizonLine = false,
                HasGeometricShapes = !string.IsNullOrEmpty(BackgroundShape) && BackgroundShape != "Rectangle",
                ColorTemperature = EstimateColorTemperature(),
                ContrastRatio = CalculateContrastRatio()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating canvas analysis data");
            return new CanvasAnalysisData(); // Return default data
        }
    }

    /// <summary>
    /// Calculates estimated memory usage for the current clock design
    /// Last Updated: 2025-01-09 23:50:00 UTC
    /// </summary>
    private double CalculateEstimatedMemoryUsage()
    {
        try
        {
            // Base memory for canvas
            var baseMemory = (ClockWidth * ClockHeight * 4) / (1024.0 * 1024.0); // RGBA bytes to MB

            // Additional memory for effects
            var effectsMultiplier = 1.0;
            if (Enable3D) effectsMultiplier += 0.5;
            if (EnableGlow) effectsMultiplier += 0.3;
            if (EnableShadow) effectsMultiplier += 0.2;
            if (EnableLighting) effectsMultiplier += 0.4;

            return baseMemory * effectsMultiplier * ClockLayers.Count;
        }
        catch
        {
            return 50.0; // Default estimate
        }
    }

    /// <summary>
    /// Calculates average brightness of the current design
    /// Last Updated: 2025-01-09 23:50:00 UTC
    /// </summary>
    private double CalculateAverageBrightness()
    {
        try
        {
            var totalBrightness = 0.0;
            var colorCount = 0;

            // Analyze text colors
            if (TextColor is SolidColorBrush textBrush)
            {
                totalBrightness += CalculateColorBrightness(textBrush.Color);
                colorCount++;
            }

            if (BackgroundColor is SolidColorBrush bgBrush && !TransparentBackground)
            {
                totalBrightness += CalculateColorBrightness(bgBrush.Color);
                colorCount++;
            }

            return colorCount > 0 ? totalBrightness / colorCount : 0.5;
        }
        catch
        {
            return 0.5; // Default brightness
        }
    }

    /// <summary>
    /// Calculates brightness of a color (0.0 to 1.0)
    /// Last Updated: 2025-01-09 23:50:00 UTC
    /// </summary>
    private static double CalculateColorBrightness(System.Windows.Media.Color color)
    {
        // Use luminance formula
        return (0.299 * color.R + 0.587 * color.G + 0.114 * color.B) / 255.0;
    }

    /// <summary>
    /// Estimates color temperature of the current design
    /// Last Updated: 2025-01-09 23:50:00 UTC
    /// </summary>
    private int EstimateColorTemperature()
    {
        try
        {
            if (TextColor is SolidColorBrush textBrush)
            {
                var color = textBrush.Color;
                var ratio = color.R / Math.Max(color.B, 1.0);

                if (ratio > 1.2) return 3000; // Warm
                if (ratio > 1.0) return 4000; // Neutral warm
                if (ratio > 0.9) return 5500; // Daylight
                return 6500; // Cool
            }

            return 5500; // Default daylight
        }
        catch
        {
            return 5500;
        }
    }

    /// <summary>
    /// Calculates contrast ratio of the current design
    /// Last Updated: 2025-01-09 23:50:00 UTC
    /// </summary>
    private double CalculateContrastRatio()
    {
        try
        {
            if (TextColor is SolidColorBrush textBrush &&
                BackgroundColor is SolidColorBrush bgBrush &&
                !TransparentBackground)
            {
                var textBrightness = CalculateColorBrightness(textBrush.Color);
                var bgBrightness = CalculateColorBrightness(bgBrush.Color);

                return Math.Abs(textBrightness - bgBrightness);
            }

            return 0.7; // Default contrast
        }
        catch
        {
            return 0.7;
        }
    }

    private static SKColor ToSKColor(System.Windows.Media.Brush brush)
    {
        if (brush is SolidColorBrush solidBrush)
        {
            var color = solidBrush.Color;
            return new SKColor(color.R, color.G, color.B, color.A);
        }
        return SKColors.White;
    }

    /// <summary>
    /// Applies high-confidence AI suggestions automatically
    /// Last Updated: 2025-01-09 23:50:00 UTC
    /// </summary>
    private async Task ApplyHighConfidenceSuggestionsAsync()
    {
        try
        {
            var appliedCount = 0;

            foreach (var suggestion in CurrentAISuggestions)
            {
                if (suggestion.ImpactScore >= AiConfidenceThreshold)
                {
                    await ApplyAISuggestion(suggestion);
                    appliedCount++;
                }
            }

            if (appliedCount > 0)
            {
                _logger.LogInformation("Auto-applied {Count} high-confidence AI suggestions", appliedCount);
                AiSuggestionStatus = $"Auto-applied {appliedCount} suggestions";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying high-confidence AI suggestions");
        }
    }

    /// <summary>
    /// Applies selection optimization suggestions from AI
    /// Last Updated: 2025-01-09 23:50:00 UTC
    /// </summary>
    private void ApplySelectionOptimizationSuggestion(UISuggestion suggestion)
    {
        try
        {
            if (suggestion.Implementation.TryGetValue("PreferredSelectionType", out var selectionTypeObj) &&
                Enum.TryParse<SelectionType>(selectionTypeObj.ToString(), out var selectionType))
            {
                SelectedSelectionTool = selectionType;
            }

            if (suggestion.Implementation.TryGetValue("EnableQuickSelection", out var quickSelectionObj) &&
                bool.TryParse(quickSelectionObj.ToString(), out var enableQuickSelection))
            {
                SelectionToolsEnabled = enableQuickSelection;
            }

            _logger.LogInformation("Applied selection optimization suggestion");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying selection optimization suggestion");
        }
    }

    /// <summary>
    /// Applies brush recommendation suggestions from AI
    /// Last Updated: 2025-01-09 23:50:00 UTC
    /// </summary>
    private void ApplyBrushRecommendationSuggestion(UISuggestion suggestion)
    {
        try
        {
            if (suggestion.Implementation.TryGetValue("SuggestedOpacity", out var opacityObj) &&
                double.TryParse(opacityObj.ToString(), out var opacity))
            {
                TextOpacity = opacity;
            }

            if (suggestion.Implementation.TryGetValue("EnablePressureSensitivity", out var pressureObj) &&
                bool.TryParse(pressureObj.ToString(), out var enablePressure))
            {
                // Apply pressure sensitivity if supported
                _logger.LogDebug("Pressure sensitivity suggestion: {Enabled}", enablePressure);
            }

            _logger.LogInformation("Applied brush recommendation suggestion");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying brush recommendation suggestion");
        }
    }

    /// <summary>
    /// Applies performance optimization suggestions from AI
    /// Last Updated: 2025-01-09 23:50:00 UTC
    /// </summary>
    private void ApplyPerformanceOptimizationSuggestion(UISuggestion suggestion)
    {
        try
        {
            if (suggestion.Implementation.TryGetValue("EnableGPUAcceleration", out var gpuObj) &&
                bool.TryParse(gpuObj.ToString(), out var enableGpu))
            {
                // GPU acceleration would be applied at renderer level
                _logger.LogDebug("GPU acceleration suggestion: {Enabled}", enableGpu);
            }

            if (suggestion.Implementation.TryGetValue("ReducePreviewQuality", out var qualityObj) &&
                bool.TryParse(qualityObj.ToString(), out var reduceQuality))
            {
                if (reduceQuality && PreviewScale > 50)
                {
                    PreviewScale = Math.Max(50, PreviewScale - 25);
                }
            }

            _logger.LogInformation("Applied performance optimization suggestion");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying performance optimization suggestion");
        }
    }

    /// <summary>
    /// Applies color theme adaptation suggestions from AI
    /// Last Updated: 2025-01-09 23:50:00 UTC
    /// </summary>
    private void ApplyColorThemeAdaptationSuggestion(UISuggestion suggestion)
    {
        try
        {
            if (suggestion.Implementation.TryGetValue("AccentColor", out var accentColorObj) &&
                accentColorObj.ToString() is string colorString)
            {
                try
                {
                    var color = (System.Windows.Media.Color)System.Windows.Media.ColorConverter.ConvertFromString(colorString);
                    TextColor = new SolidColorBrush(color);
                }
                catch
                {
                    _logger.LogWarning("Invalid accent color format: {Color}", colorString);
                }
            }

            if (suggestion.Implementation.TryGetValue("ColorScheme", out var schemeObj) &&
                schemeObj.ToString() == "AdaptiveContrast")
            {
                // Apply adaptive contrast adjustments
                ApplyAdaptiveContrastAdjustments();
            }

            _logger.LogInformation("Applied color theme adaptation suggestion");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying color theme adaptation suggestion");
        }
    }

    /// <summary>
    /// Applies adaptive contrast adjustments to improve readability
    /// Last Updated: 2025-01-09 23:50:00 UTC
    /// </summary>
    private void ApplyAdaptiveContrastAdjustments()
    {
        try
        {
            var currentContrast = CalculateContrastRatio();

            if (currentContrast < 0.5) // Low contrast
            {
                // Increase contrast by adjusting colors
                if (TextColor is SolidColorBrush textBrush)
                {
                    var textBrightness = CalculateColorBrightness(textBrush.Color);

                    if (textBrightness < 0.5)
                    {
                        // Dark text - make it darker
                        TextColor = System.Windows.Media.Brushes.Black;
                    }
                    else
                    {
                        // Light text - make it lighter
                        TextColor = System.Windows.Media.Brushes.White;
                    }
                }
            }

            _logger.LogDebug("Applied adaptive contrast adjustments");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying adaptive contrast adjustments");
        }
    }

    private SolidColorBrush? ShowColorPicker(System.Windows.Media.Brush currentBrush)
    {
        try
        {
            // Use Windows Forms ColorDialog for proper color picking
            using var colorDialog = new System.Windows.Forms.ColorDialog();

            // Set current color
            if (currentBrush is SolidColorBrush solid)
            {
                var color = solid.Color;
                colorDialog.Color = System.Drawing.Color.FromArgb(color.A, color.R, color.G, color.B);
            }

            // Show full color picker with custom colors
            colorDialog.FullOpen = true;
            colorDialog.AllowFullOpen = true;
            colorDialog.AnyColor = true;

            if (colorDialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                var selectedColor = colorDialog.Color;
                var wpfColor = System.Windows.Media.Color.FromArgb(
                    selectedColor.A, selectedColor.R, selectedColor.G, selectedColor.B);
                return new SolidColorBrush(wpfColor);
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in color picker");
            return null;
        }
    }

    /// <summary>
    /// Disposes of resources used by the ClockWorkshopViewModel
    /// Last Updated: 2025-01-11 18:30:00 UTC
    /// </summary>
    [TestableMethod("ResourceManagement", IncludeExceptionTests = true, ExpectedExecutionTimeMs = 50)]
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// Protected dispose method
    /// </summary>
    protected virtual void Dispose(bool disposing)
    {
        if (disposing)
        {
            // Dispose managed resources
            _previewUpdateTimer?.Stop();
            _previewUpdateTimer = null;

            // Dispose font loading semaphore
            _fontLoadingSemaphore?.Dispose();

            // Dispose loaded fonts
            foreach (var typeface in _loadedFonts.Values)
            {
                typeface?.Dispose();
            }
            _loadedFonts.Clear();
        }
    }
}
