// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using System.Windows;
using System.Windows.Input;
using ArtDesignFramework.ClockDesktopApp.Models;
using ArtDesignFramework.Core;
using Microsoft.Extensions.Logging;

namespace ArtDesignFramework.ClockDesktopApp.Services;

/// <summary>
/// Represents a draggable element in the preview
/// </summary>
public class DraggablePreviewElement
{
    public string Id { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public PositionableElement Element { get; set; } = new();
    public ElementType Type { get; set; }
    public bool IsVisible { get; set; } = true;
}

/// <summary>
/// Types of draggable elements
/// </summary>
public enum ElementType
{
    Time,
    Date,
    Day,
    Month,
    Year,
    AmPm,
    Separator
}

/// <summary>
/// Represents the current drag operation state
/// </summary>
public class DragState
{
    public bool IsDragging { get; set; }
    public DraggablePreviewElement? DraggedElement { get; set; }
    public System.Windows.Point StartPosition { get; set; }
    public System.Windows.Point CurrentPosition { get; set; }
    public System.Windows.Point ElementStartOffset { get; set; }
}

/// <summary>
/// Service for interactive drag-and-drop positioning of clock elements in preview
/// </summary>
[Testable]
public class DragDropPreviewService
{
    private readonly ILogger<DragDropPreviewService> _logger;
    private readonly List<DraggablePreviewElement> _elements = new();
    private DragState _dragState = new();

    // Grid settings for snap-to-grid functionality
    public double GridSize { get; set; } = 5.0; // 5 pixel grid
    public bool SnapToGridEnabled { get; set; } = true;
    public bool ShowGrid { get; set; } = false;

    // Events for UI updates
    public event Action<DraggablePreviewElement>? ElementPositionChanged;
    public event Action<DragState>? DragStateChanged;
    public event Action? ElementsUpdated;

    /// <summary>
    /// Initializes a new instance of the <see cref="DragDropPreviewService"/> class
    /// </summary>
    /// <param name="logger">Logger instance</param>
    public DragDropPreviewService(ILogger<DragDropPreviewService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Registers a draggable element
    /// Last Updated: 2025-01-11 18:30:00 UTC
    /// </summary>
    /// <param name="element">Element to register</param>
    [TestableMethod("ElementRegistration", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 25)]
    public void RegisterElement(DraggablePreviewElement element)
    {
        if (element == null)
        {
            _logger.LogWarning("Attempted to register null element");
            return;
        }

        // Remove existing element with same ID
        _elements.RemoveAll(e => e.Id == element.Id);

        // Add new element
        _elements.Add(element);
        _logger.LogDebug("Registered draggable element: {Id} ({Type})", element.Id, element.Type);

        ElementsUpdated?.Invoke();
    }

    /// <summary>
    /// Unregisters a draggable element
    /// </summary>
    /// <param name="elementId">ID of element to unregister</param>
    public void UnregisterElement(string elementId)
    {
        var removed = _elements.RemoveAll(e => e.Id == elementId);
        if (removed > 0)
        {
            _logger.LogDebug("Unregistered draggable element: {Id}", elementId);
            ElementsUpdated?.Invoke();
        }
    }

    /// <summary>
    /// Performs hit-testing to find element at given point
    /// </summary>
    /// <param name="point">Point to test</param>
    /// <returns>Hit element or null if none found</returns>
    public DraggablePreviewElement? HitTest(System.Windows.Point point)
    {
        try
        {
            // Test elements in reverse order (top-most first)
            for (int i = _elements.Count - 1; i >= 0; i--)
            {
                var element = _elements[i];
                if (element.IsVisible && element.Element.IsDraggable && element.Element.IsPointInside(point))
                {
                    _logger.LogDebug("Hit test found element: {Id} at ({X:F1}, {Y:F1})",
                        element.Id, point.X, point.Y);
                    return element;
                }
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during hit testing at point ({X:F1}, {Y:F1})", point.X, point.Y);
            return null;
        }
    }

    /// <summary>
    /// Starts a drag operation
    /// </summary>
    /// <param name="element">Element to drag</param>
    /// <param name="startPoint">Starting mouse position</param>
    /// <returns>True if drag started successfully</returns>
    public bool StartDrag(DraggablePreviewElement element, System.Windows.Point startPoint)
    {
        if (element == null)
        {
            _logger.LogWarning("Attempted to start drag with null element");
            return false;
        }

        if (_dragState.IsDragging)
        {
            _logger.LogWarning("Attempted to start drag while already dragging");
            return false;
        }

        try
        {
            _dragState.IsDragging = true;
            _dragState.DraggedElement = element;
            _dragState.StartPosition = startPoint;
            _dragState.CurrentPosition = startPoint;
            _dragState.ElementStartOffset = new System.Windows.Point(element.Element.OffsetX, element.Element.OffsetY);

            _logger.LogDebug("Started dragging element: {Id} from ({X:F1}, {Y:F1})",
                element.Id, startPoint.X, startPoint.Y);

            DragStateChanged?.Invoke(_dragState);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting drag operation for element: {Id}", element.Id);
            _dragState.IsDragging = false;
            _dragState.DraggedElement = null;
            return false;
        }
    }

    /// <summary>
    /// Updates the drag operation with new mouse position
    /// </summary>
    /// <param name="currentPoint">Current mouse position</param>
    public void UpdateDrag(System.Windows.Point currentPoint)
    {
        if (!_dragState.IsDragging || _dragState.DraggedElement == null)
            return;

        try
        {
            _dragState.CurrentPosition = currentPoint;

            // Calculate new position
            var deltaX = currentPoint.X - _dragState.StartPosition.X;
            var deltaY = currentPoint.Y - _dragState.StartPosition.Y;

            var newX = _dragState.ElementStartOffset.X + deltaX;
            var newY = _dragState.ElementStartOffset.Y + deltaY;

            // Apply snap-to-grid if enabled
            if (SnapToGridEnabled)
            {
                var snappedPosition = SnapToGrid(new System.Windows.Point(newX, newY));
                newX = snappedPosition.X;
                newY = snappedPosition.Y;
            }

            // Update element position
            _dragState.DraggedElement.Element.UpdatePosition(newX, newY);

            _logger.LogDebug("Updated drag position for {Id}: ({X:F1}, {Y:F1})",
                _dragState.DraggedElement.Id, newX, newY);

            // Notify listeners
            DragStateChanged?.Invoke(_dragState);
            ElementPositionChanged?.Invoke(_dragState.DraggedElement);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating drag operation");
        }
    }

    /// <summary>
    /// Ends the current drag operation
    /// </summary>
    public void EndDrag()
    {
        if (!_dragState.IsDragging)
            return;

        try
        {
            var draggedElement = _dragState.DraggedElement;

            _logger.LogDebug("Ended drag operation for element: {Id} at ({X:F1}, {Y:F1})",
                draggedElement?.Id, draggedElement?.Element.OffsetX, draggedElement?.Element.OffsetY);

            // Reset drag state
            _dragState.IsDragging = false;
            _dragState.DraggedElement = null;
            _dragState.StartPosition = new System.Windows.Point();
            _dragState.CurrentPosition = new System.Windows.Point();
            _dragState.ElementStartOffset = new System.Windows.Point();

            DragStateChanged?.Invoke(_dragState);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ending drag operation");
        }
    }

    /// <summary>
    /// Snaps a position to the grid
    /// </summary>
    /// <param name="position">Position to snap</param>
    /// <returns>Snapped position</returns>
    public System.Windows.Point SnapToGrid(System.Windows.Point position)
    {
        if (GridSize <= 0)
            return position;

        var snappedX = Math.Round(position.X / GridSize) * GridSize;
        var snappedY = Math.Round(position.Y / GridSize) * GridSize;

        return new System.Windows.Point(snappedX, snappedY);
    }

    /// <summary>
    /// Gets all registered elements
    /// </summary>
    /// <returns>List of all elements</returns>
    public IReadOnlyList<DraggablePreviewElement> GetElements()
    {
        return _elements.AsReadOnly();
    }

    /// <summary>
    /// Gets the current drag state
    /// </summary>
    /// <returns>Current drag state</returns>
    public DragState GetDragState()
    {
        return _dragState;
    }

    /// <summary>
    /// Updates element visibility
    /// </summary>
    /// <param name="elementId">Element ID</param>
    /// <param name="isVisible">Visibility state</param>
    public void SetElementVisibility(string elementId, bool isVisible)
    {
        var element = _elements.FirstOrDefault(e => e.Id == elementId);
        if (element != null)
        {
            element.IsVisible = isVisible;
            _logger.LogDebug("Set element {Id} visibility to {IsVisible}", elementId, isVisible);
            ElementsUpdated?.Invoke();
        }
    }

    /// <summary>
    /// Clears all registered elements
    /// </summary>
    public void ClearElements()
    {
        _elements.Clear();
        _logger.LogDebug("Cleared all draggable elements");
        ElementsUpdated?.Invoke();
    }
}
