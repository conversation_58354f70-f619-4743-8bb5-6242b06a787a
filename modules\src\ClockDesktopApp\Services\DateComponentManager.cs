// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using System.Globalization;
using System.Text.RegularExpressions;
using ArtDesignFramework.ClockDesktopApp.Models;
using ArtDesignFramework.Core;
using Microsoft.Extensions.Logging;
using SkiaSharp;

namespace ArtDesignFramework.ClockDesktopApp.Services;

/// <summary>
/// Represents a parsed date component with its format and position
/// </summary>
public class DateComponent
{
    public string Format { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
    public DateComponentType Type { get; set; }
    public PositionableElement Element { get; set; } = new();
}

/// <summary>
/// Types of date components
/// </summary>
public enum DateComponentType
{
    Day,
    Month,
    Year,
    Separator,
    Time,
    AmPm
}

/// <summary>
/// Service for handling granular date rendering with individual day, month, year components
/// </summary>
[Testable]
public class DateComponentManager
{
    private readonly ILogger<DateComponentManager> _logger;
    private readonly Dictionary<string, List<DateComponent>> _formatCache = new();

    /// <summary>
    /// Predefined date format presets like the samples in the image
    /// </summary>
    public static readonly string[] DateFormatPresets = new[]
    {
        "dddd, MMMM d, yyyy",           // Monday, September 12th, 2025
        "MM/dd | HH:mm:ss",             // 01/12 | 07:24:31
        "ddd MMM d",                    // Fri Jan 10
        "MM.dd.yy",                     // 01.12.25
        "HH:mm:ss",                     // 07:24:31
        "hh:mm tt",                     // 12:34 AM
        "HH:mm",                        // 12:34
        "MMM d, yyyy",                  // Jan 12, 2025
        "dd/MM/yyyy",                   // 12/01/2025
        "yyyy-MM-dd",                   // 2025-01-12
        "MMMM d",                       // January 12
        "ddd, MMM d",                   // Mon, Jan 12
        "HH:mm | dd.MM",                // 12:34 | 12.01
        "mm:ss",                        // 34:56 (minutes:seconds)
        "tt",                           // AM/PM only
        "dddd",                         // Monday (day only)
        "MMMM yyyy"                     // January 2025
    };

    /// <summary>
    /// Initializes a new instance of the <see cref="DateComponentManager"/> class
    /// </summary>
    /// <param name="logger">Logger instance</param>
    public DateComponentManager(ILogger<DateComponentManager> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Parses a date format string into individual components
    /// Last Updated: 2025-01-11 18:30:00 UTC
    /// </summary>
    /// <param name="format">Date format string</param>
    /// <returns>List of date components</returns>
    [TestableMethod("DateParsing", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 100)]
    public List<DateComponent> ParseDateFormat(string format)
    {
        if (string.IsNullOrWhiteSpace(format))
        {
            _logger.LogWarning("Empty or null date format provided");
            return new List<DateComponent>();
        }

        // Check cache first
        if (_formatCache.TryGetValue(format, out var cachedComponents))
        {
            return cachedComponents.Select(c => new DateComponent
            {
                Format = c.Format,
                Type = c.Type,
                Element = new PositionableElement()
            }).ToList();
        }

        var components = new List<DateComponent>();

        try
        {
            // Define regex patterns for different date/time components
            var patterns = new[]
            {
                new { Pattern = @"dddd", Type = DateComponentType.Day },      // Full day name
                new { Pattern = @"ddd", Type = DateComponentType.Day },       // Short day name
                new { Pattern = @"dd", Type = DateComponentType.Day },        // Day with leading zero
                new { Pattern = @"d", Type = DateComponentType.Day },         // Day without leading zero
                new { Pattern = @"MMMM", Type = DateComponentType.Month },    // Full month name
                new { Pattern = @"MMM", Type = DateComponentType.Month },     // Short month name
                new { Pattern = @"MM", Type = DateComponentType.Month },      // Month with leading zero
                new { Pattern = @"M", Type = DateComponentType.Month },       // Month without leading zero
                new { Pattern = @"yyyy", Type = DateComponentType.Year },     // Full year
                new { Pattern = @"yy", Type = DateComponentType.Year },       // Short year
                new { Pattern = @"HH:mm:ss", Type = DateComponentType.Time }, // 24-hour time with seconds
                new { Pattern = @"hh:mm:ss", Type = DateComponentType.Time }, // 12-hour time with seconds
                new { Pattern = @"HH:mm", Type = DateComponentType.Time },    // 24-hour time
                new { Pattern = @"hh:mm", Type = DateComponentType.Time },    // 12-hour time
                new { Pattern = @"mm:ss", Type = DateComponentType.Time },    // Minutes:seconds
                new { Pattern = @"tt", Type = DateComponentType.AmPm },       // AM/PM
            };

            var remainingFormat = format;
            var position = 0;

            while (position < format.Length)
            {
                var matched = false;

                // Try to match each pattern at the current position
                foreach (var pattern in patterns.OrderByDescending(p => p.Pattern.Length))
                {
                    var regex = new Regex($"^{Regex.Escape(pattern.Pattern)}", RegexOptions.IgnoreCase);
                    var match = regex.Match(remainingFormat.Substring(position));

                    if (match.Success)
                    {
                        components.Add(new DateComponent
                        {
                            Format = match.Value,
                            Type = pattern.Type,
                            Element = new PositionableElement()
                        });

                        position += match.Length;
                        matched = true;
                        break;
                    }
                }

                // If no pattern matched, treat as separator
                if (!matched)
                {
                    var separatorLength = 1;
                    var separatorChar = format[position].ToString();

                    // Extend separator if multiple consecutive non-pattern characters
                    while (position + separatorLength < format.Length &&
                           !patterns.Any(p => format.Substring(position + separatorLength).StartsWith(p.Pattern, StringComparison.OrdinalIgnoreCase)))
                    {
                        separatorChar += format[position + separatorLength];
                        separatorLength++;
                    }

                    components.Add(new DateComponent
                    {
                        Format = separatorChar,
                        Type = DateComponentType.Separator,
                        Element = new PositionableElement()
                    });

                    position += separatorLength;
                }
            }

            // Cache the parsed components
            _formatCache[format] = components.Select(c => new DateComponent
            {
                Format = c.Format,
                Type = c.Type,
                Element = new PositionableElement()
            }).ToList();

            _logger.LogDebug("Parsed date format '{Format}' into {Count} components", format, components.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing date format: {Format}", format);
            return new List<DateComponent>();
        }

        return components;
    }

    /// <summary>
    /// Renders individual date components with positioning and effects
    /// </summary>
    /// <param name="canvas">Canvas to render on</param>
    /// <param name="component">Date component to render</param>
    /// <param name="dateTime">Current date/time</param>
    /// <param name="paint">Base paint for rendering</param>
    /// <param name="settings">Clock settings</param>
    /// <param name="baseX">Base X position</param>
    /// <param name="baseY">Base Y position</param>
    public void RenderDateComponent(SKCanvas canvas, DateComponent component, DateTime dateTime,
        SKPaint paint, ClockSettings settings, float baseX, float baseY)
    {
        if (component == null || canvas == null || paint == null || settings == null)
        {
            _logger.LogWarning("Invalid parameters for RenderDateComponent");
            return;
        }

        try
        {
            // Get the formatted value for this component
            component.Value = GetComponentValue(component, dateTime);

            if (string.IsNullOrEmpty(component.Value))
                return;

            // Apply component-specific visibility settings
            if (!ShouldRenderComponent(component, settings))
                return;

            // Calculate final position with offsets
            var finalX = baseX + (float)component.Element.OffsetX + GetComponentOffset(component, settings, true);
            var finalY = baseY + (float)component.Element.OffsetY + GetComponentOffset(component, settings, false);

            // Create component-specific paint
            using var componentPaint = paint.Clone();
            ApplyComponentStyling(componentPaint, component, settings);

            // Update hit-test bounds for interactive positioning
            var textBounds = new SKRect();
            componentPaint.MeasureText(component.Value, ref textBounds);
            component.Element.UpdateHitTestBounds(new System.Windows.Rect(
                finalX, finalY - textBounds.Height, textBounds.Width, textBounds.Height));

            // Render the component
            canvas.DrawText(component.Value, finalX, finalY, componentPaint);

            _logger.LogDebug("Rendered component '{Value}' at ({X:F1}, {Y:F1})",
                component.Value, finalX, finalY);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rendering date component: {Format}", component.Format);
        }
    }

    /// <summary>
    /// Applies component positioning using PositionableElement
    /// Last Updated: 2025-01-11 18:30:00 UTC
    /// </summary>
    /// <param name="component">Component to position</param>
    /// <param name="x">X position</param>
    /// <param name="y">Y position</param>
    [TestableMethod("ComponentPositioning", IncludeParameterValidation = true, IncludeExceptionTests = true, ExpectedExecutionTimeMs = 25)]
    public void ApplyComponentPosition(DateComponent component, double x, double y)
    {
        if (component?.Element == null)
        {
            _logger.LogWarning("Invalid component for positioning");
            return;
        }

        component.Element.UpdatePosition(x, y);
        _logger.LogDebug("Updated component position to ({X:F1}, {Y:F1})", x, y);
    }

    /// <summary>
    /// Gets the formatted value for a date component
    /// </summary>
    private string GetComponentValue(DateComponent component, DateTime dateTime)
    {
        try
        {
            // For separator components, return the format string directly (no DateTime formatting needed)
            if (component.Type == DateComponentType.Separator)
            {
                return component.Format;
            }

            // For date/time components, format using DateTime
            return dateTime.ToString(component.Format, CultureInfo.CurrentCulture);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error formatting component with format '{Format}'", component.Format);
            return component.Format; // Fallback to the format string itself
        }
    }

    /// <summary>
    /// Determines if a component should be rendered based on settings
    /// </summary>
    private bool ShouldRenderComponent(DateComponent component, ClockSettings settings)
    {
        return component.Type switch
        {
            DateComponentType.Day => settings.ShowDay,
            DateComponentType.Month => settings.ShowMonth,
            DateComponentType.Year => settings.ShowYear,
            DateComponentType.AmPm => settings.ShowAmPm,
            DateComponentType.Time => true, // Always show time components
            DateComponentType.Separator => true, // Always show separators
            _ => true
        };
    }

    /// <summary>
    /// Gets component-specific offset from settings
    /// </summary>
    private float GetComponentOffset(DateComponent component, ClockSettings settings, bool isX)
    {
        return component.Type switch
        {
            DateComponentType.Day => isX ? (float)settings.DayOffsetX : (float)settings.DayOffsetY,
            DateComponentType.Month => isX ? (float)settings.MonthOffsetX : (float)settings.MonthOffsetY,
            DateComponentType.Year => isX ? (float)settings.YearOffsetX : (float)settings.YearOffsetY,
            DateComponentType.AmPm => isX ? (float)settings.AmPmOffsetX : (float)settings.AmPmOffsetY,
            _ => 0f
        };
    }

    /// <summary>
    /// Applies component-specific styling
    /// </summary>
    private void ApplyComponentStyling(SKPaint paint, DateComponent component, ClockSettings settings)
    {
        // Apply component-specific colors and sizes
        switch (component.Type)
        {
            case DateComponentType.Day:
                paint.Color = ToSKColor(settings.DateColor);
                paint.TextSize = (float)settings.DayFontSize;
                break;
            case DateComponentType.Month:
                paint.Color = ToSKColor(settings.DateColor);
                paint.TextSize = (float)settings.MonthFontSize;
                break;
            case DateComponentType.Year:
                paint.Color = ToSKColor(settings.DateColor);
                paint.TextSize = (float)settings.YearFontSize;
                break;
            case DateComponentType.AmPm:
                paint.Color = ToSKColor(settings.AmPmColor);
                paint.TextSize = (float)settings.AmPmFontSize;
                break;
            case DateComponentType.Time:
                // Keep original time styling
                break;
            case DateComponentType.Separator:
                paint.Color = ToSKColor(settings.DateColor);
                paint.TextSize = (float)settings.DateFontSize;
                break;
        }
    }

    /// <summary>
    /// Converts WPF Brush to SKColor
    /// </summary>
    private SKColor ToSKColor(System.Windows.Media.Brush brush)
    {
        if (brush is System.Windows.Media.SolidColorBrush solidBrush)
        {
            var color = solidBrush.Color;
            return new SKColor(color.R, color.G, color.B, color.A);
        }
        return SKColors.White; // Default fallback
    }
}
